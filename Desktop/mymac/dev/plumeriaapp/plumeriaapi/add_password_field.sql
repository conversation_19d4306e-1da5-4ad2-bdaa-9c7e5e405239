-- Add password field to staffs table for mobile authentication
ALTER TABLE staffs 
ADD COLUMN password VARCHAR(255) NULL AFTER profile_picture,
ADD COLUMN password_reset_token VARCHAR(255) NULL AFTER password,
ADD COLUMN password_reset_expires TIMESTAMP NULL AFTER password_reset_token,
ADD COLUMN last_login TIMESTAMP NULL AFTER password_reset_expires,
ADD COLUMN login_attempts INT DEFAULT 0 AFTER last_login,
ADD COLUMN account_locked_until TIMESTAMP NULL AFTER login_attempts,
ADD COLUMN mobile_verified BOOLEAN DEFAULT FALSE AFTER account_locked_until,
ADD COLUMN mobile_verification_token VARCHAR(6) NULL AFTER mobile_verified,
ADD COLUMN mobile_verification_expires TIMESTAMP NULL AFTER mobile_verification_token;

-- Add indexes for performance
CREATE INDEX idx_staffs_mobile_password ON staffs(staff_mobile, password);
CREATE INDEX idx_staffs_password_reset_token ON staffs(password_reset_token);
CREATE INDEX idx_staffs_mobile_verification ON staffs(mobile_verification_token);

-- Show the updated table structure
DESC staffs;
