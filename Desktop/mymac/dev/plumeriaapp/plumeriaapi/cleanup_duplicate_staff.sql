-- Check for duplicate staff records
SELECT 
    staff_email, 
    staff_mobile, 
    COUNT(*) as count,
    GROUP_CONCAT(id) as ids
FROM staffs 
GROUP BY staff_email, staff_mobile 
HAVING COUNT(*) > 1;

-- Check total staff count
SELECT COUNT(*) as total_staff FROM staffs;

-- Show all staff with basic info
SELECT 
    id, 
    staff_name, 
    staff_email, 
    staff_mobile, 
    created_at,
    is_active
FROM staffs 
ORDER BY created_at DESC;

-- Delete duplicate staff records (keeping the first one for each email/mobile combination)
-- UNCOMMENT THE LINES BELOW TO ACTUALLY DELETE DUPLICATES

-- DELETE s1 FROM staffs s1
-- INNER JOIN staffs s2 
-- WHERE s1.id > s2.id 
-- AND s1.staff_email = s2.staff_email 
-- AND s1.staff_mobile = s2.staff_mobile;

-- Alternative: Delete all but the latest record for each email/mobile combination
-- DELETE s1 FROM staffs s1
-- INNER JOIN (
--     SELECT staff_email, staff_mobile, MAX(id) as max_id
--     FROM staffs
--     GROUP BY staff_email, staff_mobile
--     HAVING COUNT(*) > 1
-- ) s2 ON s1.staff_email = s2.staff_email 
--    AND s1.staff_mobile = s2.staff_mobile 
--    AND s1.id < s2.max_id;
