-- Add staff module to the system
-- This script adds the staff module and assigns permissions

-- Insert staff module if it doesn't exist
INSERT IGNORE INTO modules (name, description, is_active, created_by) 
VALUES ('staff', 'Staff Management - Manage employee records, profiles, and information', 1, 1);

-- Get the module ID
SET @staff_module_id = (SELECT id FROM modules WHERE name = 'staff');

-- Get admin role ID (assuming admin role has ID 1)
SET @admin_role_id = 1;

-- Assign all permissions to admin role for staff module
INSERT IGNORE INTO role_module_permissions (role_id, module_id, permission_id) VALUES
(@admin_role_id, @staff_module_id, 1), -- create
(@admin_role_id, @staff_module_id, 2), -- read
(@admin_role_id, @staff_module_id, 3), -- update
(@admin_role_id, @staff_module_id, 4); -- delete

-- Verify the setup
SELECT 
    m.name as module_name,
    r.name as role_name,
    p.name as permission_name
FROM role_module_permissions rmp
JOIN modules m ON rmp.module_id = m.id
JOIN roles r ON rmp.role_id = r.id
JOIN permissions p ON rmp.permission_id = p.id
WHERE m.name = 'staff'
ORDER BY r.name, p.name;
