// src/models/masters/bloodgroup.model.js
const db = require('../../config/database');

const BloodGroup = {
  // Get all blood groups
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT bg.*,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM blood_groups bg
      LEFT JOIN users u1 ON bg.created_by = u1.id
      LEFT JOIN users u2 ON bg.updated_by = u2.id
    `;

    if (!includeInactive) {
      query += ' WHERE bg.is_active = 1';
    }

    return await db.query(query);
  },

  // Get blood group by ID
  findById: async (id) => {
    const query = `
      SELECT bg.*,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM blood_groups bg
      LEFT JOIN users u1 ON bg.created_by = u1.id
      LEFT JOIN users u2 ON bg.updated_by = u2.id
      WHERE bg.id = ?
    `;

    const bloodGroups = await db.query(query, [id]);
    return bloodGroups[0];
  },

  // Get blood group by name
  findByName: async (name) => {
    const query = 'SELECT * FROM blood_groups WHERE name = ?';
    const bloodGroups = await db.query(query, [name]);
    return bloodGroups[0];
  },

  // Get blood group by code
  findByCode: async (code) => {
    const query = 'SELECT * FROM blood_groups WHERE code = ?';
    const bloodGroups = await db.query(query, [code]);
    return bloodGroups[0];
  },

  // Create new blood group
  create: async (bloodGroupData) => {
    const { name, is_active = 1, created_by } = bloodGroupData;

    const query = `
      INSERT INTO blood_groups (name, is_active, created_by)
      VALUES (?, ?, ?)
    `;

    const result = await db.query(query, [name, is_active, created_by]);
    return { id: result.insertId, name, is_active, created_by };
  },

  // Update blood group
  update: async (id, bloodGroupData) => {
    const { name, is_active, updated_by } = bloodGroupData;

    let query = 'UPDATE blood_groups SET ';
    const params = [];

    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }

    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }

    query += 'updated_by = ? ';
    params.push(updated_by);

    query += 'WHERE id = ?';
    params.push(id);

    await db.query(query, params);

    return { id, ...bloodGroupData };
  },

  // Delete blood group
  delete: async (id) => {
    const query = 'DELETE FROM blood_groups WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete blood groups
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid blood group IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM blood_groups WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle blood group active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE blood_groups SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = BloodGroup;
