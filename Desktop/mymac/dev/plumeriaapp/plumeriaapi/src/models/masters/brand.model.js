// src/models/masters/brand.model.js
const db = require('../../config/database');

const Brand = {
  // Get all brands
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT b.*,
             ps.name as product_subcategory_name,
             pc.name as product_category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM brands b
      LEFT JOIN product_subcategories ps ON b.product_subcategory_id = ps.id
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON b.created_by = u1.id
      LEFT JOIN users u2 ON b.updated_by = u2.id
    `;

    if (!includeInactive) {
      query += ' WHERE b.is_active = 1';
    }

    query += ' ORDER BY b.name ASC';

    return await db.query(query);
  },

  // Get brand by ID
  findById: async (id) => {
    const query = `
      SELECT b.*,
             ps.name as product_subcategory_name,
             pc.name as product_category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM brands b
      LEFT JOIN product_subcategories ps ON b.product_subcategory_id = ps.id
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON b.created_by = u1.id
      LEFT JOIN users u2 ON b.updated_by = u2.id
      WHERE b.id = ?
    `;

    const brands = await db.query(query, [id]);
    return brands[0];
  },

  // Get brand by name and subcategory (for uniqueness check)
  findByNameAndSubcategory: async (name, product_subcategory_id) => {
    const query = 'SELECT * FROM brands WHERE name = ? AND product_subcategory_id = ?';
    const brands = await db.query(query, [name, product_subcategory_id]);
    return brands[0];
  },

  // Get brands by product subcategory
  findBySubcategory: async (product_subcategory_id, includeInactive = false) => {
    let query = `
      SELECT b.*,
             ps.name as product_subcategory_name,
             pc.name as product_category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM brands b
      LEFT JOIN product_subcategories ps ON b.product_subcategory_id = ps.id
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON b.created_by = u1.id
      LEFT JOIN users u2 ON b.updated_by = u2.id
      WHERE b.product_subcategory_id = ?
    `;

    if (!includeInactive) {
      query += ' AND b.is_active = 1';
    }

    query += ' ORDER BY b.name ASC';

    return await db.query(query, [product_subcategory_id]);
  },

  // Create new brand
  create: async (brandData) => {
    const { name, product_subcategory_id, is_active = 1, created_by } = brandData;

    const query = `
      INSERT INTO brands (name, product_subcategory_id, is_active, created_by)
      VALUES (?, ?, ?, ?)
    `;

    const result = await db.query(query, [name, product_subcategory_id, is_active, created_by]);
    return { id: result.insertId, name, product_subcategory_id, is_active, created_by };
  },

  // Update brand
  update: async (id, brandData) => {
    const { name, product_subcategory_id, is_active, updated_by } = brandData;

    const updates = [];
    const params = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }

    if (product_subcategory_id !== undefined) {
      updates.push('product_subcategory_id = ?');
      params.push(product_subcategory_id);
    }

    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }

    updates.push('updated_by = ?');
    params.push(updated_by);

    const query = `UPDATE brands SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);

    await db.query(query, params);

    return { id, ...brandData };
  },

  // Delete brand (soft delete by setting is_active to 0)
  delete: async (id, updated_by) => {
    const query = 'UPDATE brands SET is_active = 0, updated_by = ? WHERE id = ?';
    await db.query(query, [updated_by, id]);
    return { id, is_active: 0 };
  },

  // Toggle brand status
  toggleStatus: async (id, updated_by) => {
    const query = 'UPDATE brands SET is_active = NOT is_active, updated_by = ? WHERE id = ?';
    await db.query(query, [updated_by, id]);

    // Return the updated brand
    return await Brand.findById(id);
  }
};

module.exports = Brand;
