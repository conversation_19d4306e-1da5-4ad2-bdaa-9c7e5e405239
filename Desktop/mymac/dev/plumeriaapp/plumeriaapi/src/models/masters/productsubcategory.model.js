// src/models/masters/productsubcategory.model.js
const db = require('../../config/database');

const ProductSubcategory = {
  // Get all product subcategories
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT ps.*, 
             pc.name as category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM product_subcategories ps
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON ps.created_by = u1.id
      LEFT JOIN users u2 ON ps.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE ps.is_active = 1';
    }
    
    query += ' ORDER BY ps.id DESC';
    
    return await db.query(query);
  },

  // Get product subcategories by category ID
  findByCategory: async (categoryId, includeInactive = false) => {
    let query = `
      SELECT ps.*, 
             pc.name as category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM product_subcategories ps
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON ps.created_by = u1.id
      LEFT JOIN users u2 ON ps.updated_by = u2.id
      WHERE ps.category_id = ?
    `;
    
    if (!includeInactive) {
      query += ' AND ps.is_active = 1';
    }
    
    query += ' ORDER BY ps.id DESC';
    
    return await db.query(query, [categoryId]);
  },

  // Get product subcategory by ID
  findById: async (id) => {
    const query = `
      SELECT ps.*, 
             pc.name as category_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM product_subcategories ps
      LEFT JOIN product_categories pc ON ps.category_id = pc.id
      LEFT JOIN users u1 ON ps.created_by = u1.id
      LEFT JOIN users u2 ON ps.updated_by = u2.id
      WHERE ps.id = ?
    `;
    
    const productSubcategories = await db.query(query, [id]);
    return productSubcategories[0];
  },

  // Get product subcategory by name and category
  findByNameAndCategory: async (name, categoryId) => {
    const query = 'SELECT * FROM product_subcategories WHERE name = ? AND category_id = ?';
    const productSubcategories = await db.query(query, [name, categoryId]);
    return productSubcategories[0];
  },

  // Create new product subcategory
  create: async (productSubcategoryData) => {
    const { name, category_id, description = null, is_active = 1, created_by } = productSubcategoryData;
    
    const query = `
      INSERT INTO product_subcategories (name, category_id, description, is_active, created_by) 
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, category_id, description, is_active, created_by]);
    return { id: result.insertId, name, category_id, description, is_active, created_by };
  },

  // Update product subcategory
  update: async (id, productSubcategoryData) => {
    const { name, category_id, description, is_active, updated_by } = productSubcategoryData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (category_id !== undefined) {
      updates.push('category_id = ?');
      params.push(category_id);
    }
    
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE product_subcategories SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...productSubcategoryData };
  },

  // Delete product subcategory
  delete: async (id) => {
    const query = 'DELETE FROM product_subcategories WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete product subcategories
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid product subcategory IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM product_subcategories WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle product subcategory active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE product_subcategories SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = ProductSubcategory;