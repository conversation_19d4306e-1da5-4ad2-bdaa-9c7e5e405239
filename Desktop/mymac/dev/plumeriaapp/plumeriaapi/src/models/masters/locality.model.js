// src/models/masters/locality.model.js
const db = require('../../config/database');

const Locality = {
  // Get all localities
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT l.*,
             c.name as city_name,
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM localities l
      LEFT JOIN cities c ON l.city_id = c.id
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON l.created_by = u1.id
      LEFT JOIN users u2 ON l.updated_by = u2.id
    `;

    if (!includeInactive) {
      query += ' WHERE l.is_active = 1';
    }

    return await db.query(query);
  },

  // Get all localities by city
  findByCity: async (cityId, includeInactive = false) => {
    let query = `
      SELECT l.*,
             c.name as city_name,
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM localities l
      LEFT JOIN cities c ON l.city_id = c.id
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON l.created_by = u1.id
      LEFT JOIN users u2 ON l.updated_by = u2.id
      WHERE l.city_id = ?
    `;

    if (!includeInactive) {
      query += ' AND l.is_active = 1';
    }

    return await db.query(query, [cityId]);
  },

  // Get all localities by state
  findByState: async (stateId, includeInactive = false) => {
    let query = `
      SELECT l.*,
             c.name as city_name,
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM localities l
      LEFT JOIN cities c ON l.city_id = c.id
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON l.created_by = u1.id
      LEFT JOIN users u2 ON l.updated_by = u2.id
      WHERE s.id = ?
    `;

    if (!includeInactive) {
      query += ' AND l.is_active = 1';
    }

    return await db.query(query, [stateId]);
  },

  // Get locality by ID
  findById: async (id) => {
    const query = `
      SELECT l.*,
             c.name as city_name,
             s.name as state_name,
             co.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM localities l
      LEFT JOIN cities c ON l.city_id = c.id
      LEFT JOIN states s ON c.state_id = s.id
      LEFT JOIN countries co ON s.country_id = co.id
      LEFT JOIN users u1 ON l.created_by = u1.id
      LEFT JOIN users u2 ON l.updated_by = u2.id
      WHERE l.id = ?
    `;

    const localities = await db.query(query, [id]);
    return localities[0];
  },

  // Get locality by name and city
  findByName: async (name, cityId) => {
    const query = 'SELECT * FROM localities WHERE name = ? AND city_id = ?';
    const localities = await db.query(query, [name, cityId]);
    return localities[0];
  },

  // Get locality by code and city - removed as code field no longer exists

  // Create new locality
  create: async (localityData) => {
    const { name, city_id, is_active = 1, created_by } = localityData;

    const query = `
      INSERT INTO localities (name, city_id, is_active, created_by)
      VALUES (?, ?, ?, ?)
    `;

    const result = await db.query(query, [name, city_id, is_active, created_by]);
    return { id: result.insertId, name, city_id, is_active, created_by };
  },

  // Update locality
  update: async (id, localityData) => {
    const { name, city_id, is_active, updated_by } = localityData;

    let query = 'UPDATE localities SET ';
    const params = [];

    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }

    if (city_id !== undefined) {
      query += 'city_id = ?, ';
      params.push(city_id);
    }

    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }

    query += 'updated_by = ? ';
    params.push(updated_by);

    query += 'WHERE id = ?';
    params.push(id);

    await db.query(query, params);

    return { id, ...localityData };
  },

  // Delete locality
  delete: async (id) => {
    const query = 'DELETE FROM localities WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete localities
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid locality IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM localities WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle locality active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE localities SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = Locality;
