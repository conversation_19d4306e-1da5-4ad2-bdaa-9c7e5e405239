// src/controllers/masters/bloodgroup.controller.js
const BloodGroup = require('../../models/masters/bloodgroup.model');

// Get all blood groups
const getAllBloodGroups = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const bloodGroups = await BloodGroup.findAll(includeInactive);

    return res.json({
      success: true,
      data: bloodGroups
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching blood groups',
      error: error.message
    });
  }
};

// Get blood group by ID
const getBloodGroupById = async (req, res) => {
  try {
    const { id } = req.params;
    const bloodGroup = await BloodGroup.findById(id);

    if (!bloodGroup) {
      return res.status(404).json({
        success: false,
        message: 'Blood group not found'
      });
    }

    return res.json({
      success: true,
      data: bloodGroup
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching blood group',
      error: error.message
    });
  }
};

// Create a new blood group
const createBloodGroup = async (req, res) => {
  try {
    const { name, is_active } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Blood group name is required'
      });
    }

    // Check if blood group name already exists
    const existingBloodGroupWithName = await BloodGroup.findByName(name);
    if (existingBloodGroupWithName) {
      return res.status(409).json({
        success: false,
        message: 'Blood group name already exists'
      });
    }

    // Create the blood group with the authenticated user's ID
    const bloodGroup = await BloodGroup.create({
      name,
      is_active,
      created_by: req.user.id
    });

    return res.status(201).json({
      success: true,
      message: 'Blood group created successfully',
      data: bloodGroup
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating blood group',
      error: error.message
    });
  }
};

// Update a blood group
const updateBloodGroup = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, is_active } = req.body;

    // Check if blood group exists
    const bloodGroup = await BloodGroup.findById(id);
    if (!bloodGroup) {
      return res.status(404).json({
        success: false,
        message: 'Blood group not found'
      });
    }

    // Check if name is being changed and if it already exists
    if (name && name !== bloodGroup.name) {
      const existingBloodGroup = await BloodGroup.findByName(name);
      if (existingBloodGroup) {
        return res.status(409).json({
          success: false,
          message: 'Blood group name already exists'
        });
      }
    }

    // Update the blood group with the authenticated user's ID as the updater
    const updatedBloodGroup = await BloodGroup.update(id, {
      name,
      is_active,
      updated_by: req.user.id
    });

    return res.json({
      success: true,
      message: 'Blood group updated successfully',
      data: updatedBloodGroup
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating blood group',
      error: error.message
    });
  }
};

// Delete a blood group
const deleteBloodGroup = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if blood group exists
    const bloodGroup = await BloodGroup.findById(id);
    if (!bloodGroup) {
      return res.status(404).json({
        success: false,
        message: 'Blood group not found'
      });
    }

    // Delete the blood group
    await BloodGroup.delete(id);

    return res.json({
      success: true,
      message: 'Blood group deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting blood group',
      error: error.message
    });
  }
};

// Toggle blood group active status
const toggleBloodGroupStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    // Check if blood group exists
    const bloodGroup = await BloodGroup.findById(id);
    if (!bloodGroup) {
      return res.status(404).json({
        success: false,
        message: 'Blood group not found'
      });
    }

    // Toggle the status
    const updatedBloodGroup = await BloodGroup.toggleActive(id, Boolean(is_active), req.user.id);

    return res.json({
      success: true,
      message: `Blood group ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedBloodGroup
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling blood group status',
      error: error.message
    });
  }
};

// Bulk delete blood groups
const bulkDeleteBloodGroups = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of blood group IDs is required'
      });
    }

    // Perform bulk delete
    await BloodGroup.bulkDelete(ids);

    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} blood groups`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllBloodGroups,
  getBloodGroupById,
  createBloodGroup,
  updateBloodGroup,
  deleteBloodGroup,
  toggleBloodGroupStatus,
  bulkDeleteBloodGroups
};
