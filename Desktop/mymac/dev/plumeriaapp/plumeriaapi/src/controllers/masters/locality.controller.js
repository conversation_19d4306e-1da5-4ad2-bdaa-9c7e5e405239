// src/controllers/masters/locality.controller.js
const Locality = require('../../models/masters/locality.model');
const City = require('../../models/masters/city.model');

// Get all localities
const getAllLocalities = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const cityId = req.query.cityId;
    const stateId = req.query.stateId;

    let localities;
    if (cityId) {
      // Get localities by city
      localities = await Locality.findByCity(cityId, includeInactive);
    } else if (stateId) {
      // Get localities by state
      localities = await Locality.findByState(stateId, includeInactive);
    } else {
      // Get all localities
      localities = await Locality.findAll(includeInactive);
    }

    return res.json({
      success: true,
      data: localities
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching localities',
      error: error.message
    });
  }
};

// Get locality by ID
const getLocalityById = async (req, res) => {
  try {
    const { id } = req.params;
    const locality = await Locality.findById(id);

    if (!locality) {
      return res.status(404).json({
        success: false,
        message: 'Locality not found'
      });
    }

    return res.json({
      success: true,
      data: locality
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching locality',
      error: error.message
    });
  }
};

// Create a new locality
const createLocality = async (req, res) => {
  try {
    const { name, city_id, is_active } = req.body;

    if (!name || !city_id) {
      return res.status(400).json({
        success: false,
        message: 'Locality name and city_id are required'
      });
    }

    // Check if city exists
    const city = await City.findById(city_id);
    if (!city) {
      return res.status(404).json({
        success: false,
        message: 'City not found'
      });
    }

    // Check if locality name already exists for this city
    const existingLocalityWithName = await Locality.findByName(name, city_id);
    if (existingLocalityWithName) {
      return res.status(409).json({
        success: false,
        message: 'Locality name already exists for this city'
      });
    }

    // No code validation needed

    // Create the locality with the authenticated user's ID
    const localityData = {
      name,
      city_id,
      is_active,
      created_by: req.user.id
    };

    const locality = await Locality.create(localityData);

    return res.status(201).json({
      success: true,
      message: 'Locality created successfully',
      data: locality
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating locality',
      error: error.message
    });
  }
};

// Update a locality
const updateLocality = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, city_id, is_active } = req.body;

    // Check if locality exists
    const locality = await Locality.findById(id);
    if (!locality) {
      return res.status(404).json({
        success: false,
        message: 'Locality not found'
      });
    }

    // If city_id is being changed, check if the new city exists
    if (city_id && city_id !== locality.city_id) {
      const city = await City.findById(city_id);
      if (!city) {
        return res.status(404).json({
          success: false,
          message: 'City not found'
        });
      }
    }

    // Check if name is being changed and if it already exists for this city
    if (name && name !== locality.name) {
      const cityIdToCheck = city_id || locality.city_id;
      const existingLocality = await Locality.findByName(name, cityIdToCheck);
      if (existingLocality) {
        return res.status(409).json({
          success: false,
          message: 'Locality name already exists for this city'
        });
      }
    }

    // No code validation needed

    // Update the locality with the authenticated user's ID as the updater
    const localityData = {
      updated_by: req.user.id
    };

    // Only include fields that are provided
    if (name !== undefined) localityData.name = name;
    if (city_id !== undefined) localityData.city_id = city_id;
    if (is_active !== undefined) localityData.is_active = is_active;

    const updatedLocality = await Locality.update(id, localityData);

    return res.json({
      success: true,
      message: 'Locality updated successfully',
      data: updatedLocality
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating locality',
      error: error.message
    });
  }
};

// Delete a locality
const deleteLocality = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if locality exists
    const locality = await Locality.findById(id);
    if (!locality) {
      return res.status(404).json({
        success: false,
        message: 'Locality not found'
      });
    }

    // Delete the locality
    await Locality.delete(id);

    return res.json({
      success: true,
      message: 'Locality deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting locality',
      error: error.message
    });
  }
};

// Toggle locality active status
const toggleLocalityStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    // Check if locality exists
    const locality = await Locality.findById(id);
    if (!locality) {
      return res.status(404).json({
        success: false,
        message: 'Locality not found'
      });
    }

    // Toggle the status
    const updatedLocality = await Locality.toggleActive(id, Boolean(is_active), req.user.id);

    return res.json({
      success: true,
      message: `Locality ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedLocality
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling locality status',
      error: error.message
    });
  }
};

// Bulk delete localities
const bulkDeleteLocalities = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of locality IDs is required'
      });
    }

    // Perform bulk delete
    await Locality.bulkDelete(ids);

    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} localities`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllLocalities,
  getLocalityById,
  createLocality,
  updateLocality,
  deleteLocality,
  toggleLocalityStatus,
  bulkDeleteLocalities
};
