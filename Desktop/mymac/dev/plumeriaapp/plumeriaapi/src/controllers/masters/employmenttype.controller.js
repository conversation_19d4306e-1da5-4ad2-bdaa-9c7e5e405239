// src/controllers/masters/employmenttype.controller.js
const EmploymentType = require('../../models/masters/employmenttype.model');

// Get all employment types
const getAllEmploymentTypes = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const employmentTypes = await EmploymentType.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: employmentTypes
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching employment types',
      error: error.message
    });
  }
};

// Get employment type by ID
const getEmploymentTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const employmentType = await EmploymentType.findById(id);
    
    if (!employmentType) {
      return res.status(404).json({
        success: false,
        message: 'Employment type not found'
      });
    }
    
    return res.json({
      success: true,
      data: employmentType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching employment type',
      error: error.message
    });
  }
};

// Create a new employment type
const createEmploymentType = async (req, res) => {
  try {
    const { name, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Employment type name is required'
      });
    }
    
    // Check if employment type name already exists
    const existingEmploymentTypeWithName = await EmploymentType.findByName(name);
    if (existingEmploymentTypeWithName) {
      return res.status(409).json({
        success: false,
        message: 'Employment type name already exists'
      });
    }
    
    // Create the employment type with the authenticated user's ID
    const employmentType = await EmploymentType.create({
      name,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Employment type created successfully',
      data: employmentType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating employment type',
      error: error.message
    });
  }
};

// Update an employment type
const updateEmploymentType = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, is_active } = req.body;
    
    // Check if employment type exists
    const employmentType = await EmploymentType.findById(id);
    if (!employmentType) {
      return res.status(404).json({
        success: false,
        message: 'Employment type not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== employmentType.name) {
      const existingEmploymentType = await EmploymentType.findByName(name);
      if (existingEmploymentType) {
        return res.status(409).json({
          success: false,
          message: 'Employment type name already exists'
        });
      }
    }
    
    // Update the employment type with the authenticated user's ID as the updater
    const updatedEmploymentType = await EmploymentType.update(id, {
      name,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Employment type updated successfully',
      data: updatedEmploymentType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating employment type',
      error: error.message
    });
  }
};

// Delete an employment type
const deleteEmploymentType = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if employment type exists
    const employmentType = await EmploymentType.findById(id);
    if (!employmentType) {
      return res.status(404).json({
        success: false,
        message: 'Employment type not found'
      });
    }
    
    // Delete the employment type
    await EmploymentType.delete(id);
    
    return res.json({
      success: true,
      message: 'Employment type deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting employment type',
      error: error.message
    });
  }
};

// Toggle employment type active status
const toggleEmploymentTypeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if employment type exists
    const employmentType = await EmploymentType.findById(id);
    if (!employmentType) {
      return res.status(404).json({
        success: false,
        message: 'Employment type not found'
      });
    }
    
    // Toggle the status
    const updatedEmploymentType = await EmploymentType.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Employment type ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedEmploymentType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling employment type status',
      error: error.message
    });
  }
};

// Bulk delete employment types
const bulkDeleteEmploymentTypes = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of employment type IDs is required'
      });
    }
    
    // Perform bulk delete
    await EmploymentType.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} employment types`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllEmploymentTypes,
  getEmploymentTypeById,
  createEmploymentType,
  updateEmploymentType,
  deleteEmploymentType,
  toggleEmploymentTypeStatus,
  bulkDeleteEmploymentTypes
};