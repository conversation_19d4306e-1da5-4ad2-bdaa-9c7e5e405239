// src/controllers/masters/measurementunit.controller.js
const MeasurementUnit = require('../../models/masters/measurementunit.model');

// Get all measurement units
const getAllMeasurementUnits = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const measurementUnits = await MeasurementUnit.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: measurementUnits
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching measurement units',
      error: error.message
    });
  }
};

// Get measurement unit by ID
const getMeasurementUnitById = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid measurement unit ID is required'
      });
    }
    
    const measurementUnit = await MeasurementUnit.findById(id);
    
    if (!measurementUnit) {
      return res.status(404).json({
        success: false,
        message: 'Measurement unit not found'
      });
    }
    
    return res.json({
      success: true,
      data: measurementUnit
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching measurement unit',
      error: error.message
    });
  }
};

// Create a new measurement unit
const createMeasurementUnit = async (req, res) => {
  try {
    const { name, symbol, is_active } = req.body;
    
    if (!name || !symbol) {
      return res.status(400).json({
        success: false,
        message: 'Measurement unit name and symbol are required'
      });
    }
    
    // Check if measurement unit name already exists
    const existingMeasurementUnitWithName = await MeasurementUnit.findByName(name);
    if (existingMeasurementUnitWithName) {
      return res.status(409).json({
        success: false,
        message: 'Measurement unit name already exists'
      });
    }
    
    // Check if measurement unit symbol already exists
    const existingMeasurementUnitWithSymbol = await MeasurementUnit.findBySymbol(symbol);
    if (existingMeasurementUnitWithSymbol) {
      return res.status(409).json({
        success: false,
        message: 'Measurement unit symbol already exists'
      });
    }
    
    // Create the measurement unit with the authenticated user's ID
    const measurementUnit = await MeasurementUnit.create({
      name,
      symbol,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Measurement unit created successfully',
      data: measurementUnit
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating measurement unit',
      error: error.message
    });
  }
};

// Update measurement unit
const updateMeasurementUnit = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, symbol, is_active } = req.body;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid measurement unit ID is required'
      });
    }
    
    // Check if measurement unit exists
    const measurementUnit = await MeasurementUnit.findById(id);
    if (!measurementUnit) {
      return res.status(404).json({
        success: false,
        message: 'Measurement unit not found'
      });
    }
    
    // Check if name is being updated and if it already exists
    if (name && name !== measurementUnit.name) {
      const existingMeasurementUnitWithName = await MeasurementUnit.findByName(name);
      if (existingMeasurementUnitWithName) {
        return res.status(409).json({
          success: false,
          message: 'Measurement unit name already exists'
        });
      }
    }
    
    // Check if symbol is being updated and if it already exists
    if (symbol && symbol !== measurementUnit.symbol) {
      const existingMeasurementUnitWithSymbol = await MeasurementUnit.findBySymbol(symbol);
      if (existingMeasurementUnitWithSymbol) {
        return res.status(409).json({
          success: false,
          message: 'Measurement unit symbol already exists'
        });
      }
    }
    
    // Update the measurement unit
    const updatedMeasurementUnit = await MeasurementUnit.update(id, {
      name,
      symbol,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Measurement unit updated successfully',
      data: updatedMeasurementUnit
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating measurement unit',
      error: error.message
    });
  }
};

// Delete measurement unit
const deleteMeasurementUnit = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: 'Valid measurement unit ID is required'
      });
    }
    
    // Check if measurement unit exists
    const measurementUnit = await MeasurementUnit.findById(id);
    if (!measurementUnit) {
      return res.status(404).json({
        success: false,
        message: 'Measurement unit not found'
      });
    }
    
    // Delete the measurement unit
    await MeasurementUnit.delete(id);
    
    return res.json({
      success: true,
      message: 'Measurement unit deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting measurement unit',
      error: error.message
    });
  }
};

// Toggle measurement unit active status
const toggleMeasurementUnitStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if measurement unit exists
    const measurementUnit = await MeasurementUnit.findById(id);
    if (!measurementUnit) {
      return res.status(404).json({
        success: false,
        message: 'Measurement unit not found'
      });
    }
    
    // Toggle the status
    const updatedMeasurementUnit = await MeasurementUnit.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Measurement unit ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedMeasurementUnit
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling measurement unit status',
      error: error.message
    });
  }
};

// Bulk delete measurement units
const bulkDeleteMeasurementUnits = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of measurement unit IDs is required'
      });
    }
    
    // Perform bulk delete
    await MeasurementUnit.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} measurement units`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllMeasurementUnits,
  getMeasurementUnitById,
  createMeasurementUnit,
  updateMeasurementUnit,
  deleteMeasurementUnit,
  toggleMeasurementUnitStatus,
  bulkDeleteMeasurementUnits
};
