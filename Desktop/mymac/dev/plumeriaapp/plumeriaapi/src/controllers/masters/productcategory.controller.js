// src/controllers/masters/productcategory.controller.js
const ProductCategory = require('../../models/masters/productcategory.model');

// Get all product categories
const getAllProductCategories = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const productCategories = await ProductCategory.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: productCategories
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching product categories',
      error: error.message
    });
  }
};

// Get product category by ID
const getProductCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const productCategory = await ProductCategory.findById(id);
    
    if (!productCategory) {
      return res.status(404).json({
        success: false,
        message: 'Product category not found'
      });
    }
    
    return res.json({
      success: true,
      data: productCategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching product category',
      error: error.message
    });
  }
};

// Create a new product category
const createProductCategory = async (req, res) => {
  try {
    const { name, description, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Product category name is required'
      });
    }
    
    // Check if product category name already exists
    const existingProductCategoryWithName = await ProductCategory.findByName(name);
    if (existingProductCategoryWithName) {
      return res.status(409).json({
        success: false,
        message: 'Product category name already exists'
      });
    }
    
    // Create the product category with the authenticated user's ID
    const productCategory = await ProductCategory.create({
      name,
      description,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Product category created successfully',
      data: productCategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating product category',
      error: error.message
    });
  }
};

// Update a product category
const updateProductCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_active } = req.body;
    
    // Check if product category exists
    const productCategory = await ProductCategory.findById(id);
    if (!productCategory) {
      return res.status(404).json({
        success: false,
        message: 'Product category not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== productCategory.name) {
      const existingProductCategory = await ProductCategory.findByName(name);
      if (existingProductCategory) {
        return res.status(409).json({
          success: false,
          message: 'Product category name already exists'
        });
      }
    }
    
    // Update the product category with the authenticated user's ID as the updater
    const updatedProductCategory = await ProductCategory.update(id, {
      name,
      description,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Product category updated successfully',
      data: updatedProductCategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating product category',
      error: error.message
    });
  }
};

// Delete a product category
const deleteProductCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if product category exists
    const productCategory = await ProductCategory.findById(id);
    if (!productCategory) {
      return res.status(404).json({
        success: false,
        message: 'Product category not found'
      });
    }
    
    // Delete the product category
    await ProductCategory.delete(id);
    
    return res.json({
      success: true,
      message: 'Product category deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting product category',
      error: error.message
    });
  }
};

// Toggle product category active status
const toggleProductCategoryStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if product category exists
    const productCategory = await ProductCategory.findById(id);
    if (!productCategory) {
      return res.status(404).json({
        success: false,
        message: 'Product category not found'
      });
    }
    
    // Toggle the status
    const updatedProductCategory = await ProductCategory.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Product category ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedProductCategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling product category status',
      error: error.message
    });
  }
};

// Bulk delete product categories
const bulkDeleteProductCategories = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of product category IDs is required'
      });
    }
    
    // Perform bulk delete
    await ProductCategory.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} product categories`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllProductCategories,
  getProductCategoryById,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,
  toggleProductCategoryStatus,
  bulkDeleteProductCategories
};