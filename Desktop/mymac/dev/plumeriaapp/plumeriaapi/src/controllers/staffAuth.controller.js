const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const Staff = require('../models/staff.model');
const { sendSMS } = require('../utils/sms.util'); // You'll need to implement SMS service

/**
 * Staff Authentication Controller
 * Implements secure authentication for mobile app staff login
 */
const StaffAuthController = {
  /**
   * Staff login with mobile and password
   */
  login: async (req, res) => {
    try {
      const { staff_mobile, password } = req.body;

      // Validation
      if (!staff_mobile || !password) {
        return res.status(400).json({
          success: false,
          message: 'Mobile number and password are required'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return res.status(401).json({
          success: false,
          message: 'Invalid mobile number or password'
        });
      }

      // Check if account is locked
      if (staff.account_locked_until && new Date() < new Date(staff.account_locked_until)) {
        return res.status(423).json({
          success: false,
          message: 'Account is temporarily locked. Please try again later.'
        });
      }

      // Check if staff is active
      if (!staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Your account has been deactivated. Please contact administrator.'
        });
      }

      // Check if password is set
      if (!staff.password) {
        return res.status(400).json({
          success: false,
          message: 'Password not set. Please use OTP login or contact administrator.'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, staff.password);
      if (!isPasswordValid) {
        // Increment login attempts
        const newAttempts = (staff.login_attempts || 0) + 1;
        const updateData = { login_attempts: newAttempts };

        // Lock account after 5 failed attempts for 30 minutes
        if (newAttempts >= 5) {
          updateData.account_locked_until = new Date(Date.now() + 30 * 60 * 1000);
        }

        await Staff.update(staff.id, updateData);

        return res.status(401).json({
          success: false,
          message: 'Invalid mobile number or password'
        });
      }

      // Reset login attempts and update last login
      await Staff.update(staff.id, {
        login_attempts: 0,
        account_locked_until: null,
        last_login: new Date()
      });

      // Generate JWT tokens
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          type: 'staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      const refreshToken = jwt.sign(
        {
          staffId: staff.id,
          type: 'staff_refresh'
        },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
      );

      // Remove sensitive data
      const { password: _, password_reset_token, mobile_verification_token, ...staffData } = staff;

      res.json({
        success: true,
        data: {
          staff: staffData,
          token: accessToken,
          refreshToken: refreshToken
        },
        message: 'Login successful'
      });

    } catch (error) {
      console.error('Staff login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  /**
   * Send OTP for mobile verification/login
   */
  sendOTP: async (req, res) => {
    try {
      const { staff_mobile } = req.body;

      if (!staff_mobile) {
        return res.status(400).json({
          success: false,
          message: 'Mobile number is required'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found with this mobile number'
        });
      }

      if (!staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Your account has been deactivated. Please contact administrator.'
        });
      }

      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Update staff with OTP
      await Staff.update(staff.id, {
        mobile_verification_token: otp,
        mobile_verification_expires: otpExpires
      });

      // Send SMS (implement your SMS service)
      try {
        await sendSMS(staff_mobile, `Your OTP for login is: ${otp}. Valid for 10 minutes.`);
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
        // Continue without failing - in development you might want to log the OTP
        console.log(`Development OTP for ${staff_mobile}: ${otp}`);
      }

      res.json({
        success: true,
        message: 'OTP sent successfully to your mobile number'
      });

    } catch (error) {
      console.error('Send OTP error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send OTP'
      });
    }
  },

  /**
   * Verify OTP and login
   */
  verifyOTP: async (req, res) => {
    try {
      const { staff_mobile, otp } = req.body;

      if (!staff_mobile || !otp) {
        return res.status(400).json({
          success: false,
          message: 'Mobile number and OTP are required'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Check OTP
      if (!staff.mobile_verification_token || staff.mobile_verification_token !== otp) {
        return res.status(400).json({
          success: false,
          message: 'Invalid OTP'
        });
      }

      // Check OTP expiry
      if (!staff.mobile_verification_expires || new Date() > new Date(staff.mobile_verification_expires)) {
        return res.status(400).json({
          success: false,
          message: 'OTP has expired'
        });
      }

      // Clear OTP and mark mobile as verified
      await Staff.update(staff.id, {
        mobile_verification_token: null,
        mobile_verification_expires: null,
        mobile_verified: true,
        last_login: new Date()
      });

      // Generate JWT tokens
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          type: 'staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      const refreshToken = jwt.sign(
        {
          staffId: staff.id,
          type: 'staff_refresh'
        },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
      );

      // Remove sensitive data
      const { password, password_reset_token, mobile_verification_token, ...staffData } = staff;

      res.json({
        success: true,
        data: {
          staff: staffData,
          token: accessToken,
          refreshToken: refreshToken
        },
        message: 'OTP verified successfully'
      });

    } catch (error) {
      console.error('Verify OTP error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to verify OTP'
      });
    }
  },

  /**
   * Set password for staff (first time setup)
   */
  setPassword: async (req, res) => {
    try {
      const { staff_mobile, password, confirmPassword } = req.body;

      // Validation
      if (!staff_mobile || !password || !confirmPassword) {
        return res.status(400).json({
          success: false,
          message: 'Mobile number, password, and confirm password are required'
        });
      }

      if (password !== confirmPassword) {
        return res.status(400).json({
          success: false,
          message: 'Passwords do not match'
        });
      }

      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      if (!staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Your account has been deactivated'
        });
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Update staff with hashed password
      await Staff.update(staff.id, {
        password: hashedPassword,
        mobile_verified: true
      });

      res.json({
        success: true,
        message: 'Password set successfully'
      });

    } catch (error) {
      console.error('Set password error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to set password'
      });
    }
  },

  /**
   * Request password reset
   */
  requestPasswordReset: async (req, res) => {
    try {
      const { staff_mobile } = req.body;

      if (!staff_mobile) {
        return res.status(400).json({
          success: false,
          message: 'Mobile number is required'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        // Don't reveal if mobile exists or not for security
        return res.json({
          success: true,
          message: 'If the mobile number exists, a reset token has been sent'
        });
      }

      if (!staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Your account has been deactivated'
        });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

      // Update staff with reset token
      await Staff.update(staff.id, {
        password_reset_token: resetToken,
        password_reset_expires: resetExpires
      });

      // Send SMS with reset instructions
      try {
        await sendSMS(staff_mobile, `Your password reset code is: ${resetToken.substring(0, 8).toUpperCase()}. Valid for 30 minutes.`);
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
        console.log(`Development reset token for ${staff_mobile}: ${resetToken.substring(0, 8).toUpperCase()}`);
      }

      res.json({
        success: true,
        message: 'Password reset instructions sent to your mobile number'
      });

    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process password reset request'
      });
    }
  },

  /**
   * Verify reset token and set new password
   */
  verifyPasswordReset: async (req, res) => {
    try {
      const { staff_mobile, token, newPassword, confirmPassword } = req.body;

      // Validation
      if (!staff_mobile || !token || !newPassword || !confirmPassword) {
        return res.status(400).json({
          success: false,
          message: 'All fields are required'
        });
      }

      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          success: false,
          message: 'Passwords do not match'
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      // Find staff by mobile
      const staff = await Staff.findByMobile(staff_mobile);
      if (!staff) {
        return res.status(400).json({
          success: false,
          message: 'Invalid reset request'
        });
      }

      // Verify reset token
      if (!staff.password_reset_token || !staff.password_reset_token.startsWith(token.toLowerCase())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid reset token'
        });
      }

      // Check token expiry
      if (!staff.password_reset_expires || new Date() > new Date(staff.password_reset_expires)) {
        return res.status(400).json({
          success: false,
          message: 'Reset token has expired'
        });
      }

      // Hash new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update staff with new password and clear reset token
      await Staff.update(staff.id, {
        password: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
        login_attempts: 0,
        account_locked_until: null
      });

      res.json({
        success: true,
        message: 'Password reset successfully'
      });

    } catch (error) {
      console.error('Password reset verify error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reset password'
      });
    }
  },

  /**
   * Refresh JWT token
   */
  refreshToken: async (req, res) => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          message: 'Refresh token is required'
        });
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

      if (decoded.type !== 'staff_refresh') {
        return res.status(401).json({
          success: false,
          message: 'Invalid token type'
        });
      }

      // Find staff
      const staff = await Staff.findById(decoded.staffId);
      if (!staff || !staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Staff not found or inactive'
        });
      }

      // Generate new access token
      const accessToken = jwt.sign(
        {
          staffId: staff.id,
          mobile: staff.staff_mobile,
          type: 'staff'
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      res.json({
        success: true,
        data: {
          token: accessToken
        },
        message: 'Token refreshed successfully'
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }
  }
};

module.exports = StaffAuthController;
