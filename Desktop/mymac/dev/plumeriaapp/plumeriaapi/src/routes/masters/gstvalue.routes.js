// src/routes/masters/gstvalue.routes.js
const express = require('express');
const gstValueController = require('../../controllers/masters/gstvalue.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing GST values
router.get('/', hasModulePermission('masters', 'read'), gstValueController.getAllGstValues);
router.get('/:id', hasModulePermission('masters', 'read'), gstValueController.getGstValueById);
router.post('/', hasModulePermission('masters', 'create'), gstValueController.createGstValue);
router.put('/:id', hasModulePermission('masters', 'update'), gstValueController.updateGstValue);
router.delete('/:id', hasModulePermission('masters', 'delete'), gstValueController.deleteGstValue);
router.patch('/:id/status', hasModulePermission('masters', 'update'), gstValueController.toggleGstValueStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), gstValueController.bulkDeleteGstValues);

module.exports = router;
