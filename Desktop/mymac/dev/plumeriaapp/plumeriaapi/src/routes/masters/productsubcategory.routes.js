// src/routes/masters/productsubcategory.routes.js
const express = require('express');
const productSubcategoryController = require('../../controllers/masters/productsubcategory.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing product subcategories
router.get('/', hasModulePermission('masters', 'read'), productSubcategoryController.getAllProductSubcategories);
router.get('/:id', hasModulePermission('masters', 'read'), productSubcategoryController.getProductSubcategoryById);
router.post('/', hasModulePermission('masters', 'create'), productSubcategoryController.createProductSubcategory);
router.put('/:id', hasModulePermission('masters', 'update'), productSubcategoryController.updateProductSubcategory);
router.delete('/:id', hasModulePermission('masters', 'delete'), productSubcategoryController.deleteProductSubcategory);
router.patch('/:id/status', hasModulePermission('masters', 'update'), productSubcategoryController.toggleProductSubcategoryStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), productSubcategoryController.bulkDeleteProductSubcategories);

module.exports = router;