// src/routes/masters/projecttype.routes.js
const express = require('express');
const projectTypeController = require('../../controllers/masters/projecttype.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing project types
router.get('/', hasModulePermission('masters', 'read'), projectTypeController.getAllProjectTypes);
router.get('/:id', hasModulePermission('masters', 'read'), projectTypeController.getProjectTypeById);
router.post('/', hasModulePermission('masters', 'create'), projectTypeController.createProjectType);
router.put('/:id', hasModulePermission('masters', 'update'), projectTypeController.updateProjectType);
router.delete('/:id', hasModulePermission('masters', 'delete'), projectTypeController.deleteProjectType);
router.patch('/:id/status', hasModulePermission('masters', 'update'), projectTypeController.toggleProjectTypeStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), projectTypeController.bulkDeleteProjectTypes);

module.exports = router;