// src/routes/masters/qualification.routes.js
const express = require('express');
const qualificationController = require('../../controllers/masters/qualification.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing qualifications
router.get('/', hasModulePermission('masters', 'read'), qualificationController.getAllQualifications);
router.get('/:id', hasModulePermission('masters', 'read'), qualificationController.getQualificationById);
router.post('/', hasModulePermission('masters', 'create'), qualificationController.createQualification);
router.put('/:id', hasModulePermission('masters', 'update'), qualificationController.updateQualification);
router.delete('/:id', hasModulePermission('masters', 'delete'), qualificationController.deleteQualification);
router.patch('/:id/status', hasModulePermission('masters', 'update'), qualificationController.toggleQualificationStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), qualificationController.bulkDeleteQualifications);

module.exports = router;