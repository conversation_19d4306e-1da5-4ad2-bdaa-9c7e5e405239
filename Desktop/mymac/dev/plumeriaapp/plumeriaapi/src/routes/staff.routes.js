const express = require('express');
const StaffController = require('../controllers/staff.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');
const { uploadProfilePicture } = require('../middleware/upload.middleware');

const router = express.Router();

router.use(verifyToken);

router.post('/bulk-delete',
  hasModulePermission('staff', 'delete'),
  StaffController.bulkDeleteStaff
);

router.post('/bulk-status',
  hasModulePermission('staff', 'update'),
  StaffController.bulkUpdateStatus
);

router.post('/by-ids',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffByIds
);

router.get('/search',
  hasModulePermission('staff', 'read'),
  StaffController.searchStaff
);

router.get('/stats',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffStats
);

router.get('/export',
  hasModulePermission('staff', 'read'),
  StaffController.exportStaff
);

router.get('/department/:departmentId',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffByDepartment
);

router.get('/',
  hasModulePermission('staff', 'read'),
  StaffController.getAllStaff
);

router.post('/',
  hasModulePermission('staff', 'create'),
  StaffController.createStaff
);

router.get('/:id',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffById
);

router.put('/:id',
  hasModulePermission('staff', 'update'),
  StaffController.updateStaff
);

router.delete('/:id',
  hasModulePermission('staff', 'delete'),
  StaffController.deleteStaff
);

router.patch('/:id/status',
  hasModulePermission('staff', 'update'),
  StaffController.toggleStaffStatus
);

router.post('/:id/upload-photo',
  hasModulePermission('staff', 'update'),
  uploadProfilePicture,
  StaffController.uploadProfilePicture
);

router.delete('/:id/photo',
  hasModulePermission('staff', 'update'),
  StaffController.removeProfilePicture
);

router.post('/:id/change-password',
  hasModulePermission('staff', 'update'),
  StaffController.changeStaffPassword
);

router.use((error, _req, res, _next) => {
  console.error('Staff routes error:', error);

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      error: error.message
    });
  }

  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format',
      error: error.message
    });
  }

  if (error.code && error.code.startsWith('LIMIT_')) {
    return res.status(400).json({
      success: false,
      message: 'File upload error',
      error: error.message
    });
  }

  if (error.code === 'ECONNREFUSED' || error.code === 'ER_ACCESS_DENIED_ERROR') {
    return res.status(503).json({
      success: false,
      message: 'Database connection error',
      error: 'Service temporarily unavailable'
    });
  }

  if (error.message && error.message.includes('permission')) {
    return res.status(403).json({
      success: false,
      message: 'Insufficient permissions',
      error: error.message
    });
  }

  return res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

module.exports = router;