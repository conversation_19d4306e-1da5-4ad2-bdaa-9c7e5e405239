<aside class="sidebar" [class.collapsed]="collapsed">
  <div class="sidebar-content">
    <!-- User profile section -->
    <div class="user-profile" *ngIf="!collapsed">
      <div class="avatar">
        <mat-icon>account_circle</mat-icon>
      </div>
      <div class="user-info">
        <h3>{{ (authService.currentUser | async)?.name || 'Administrator' }}</h3>
        <p>{{ (authService.currentUser | async)?.roles?.[0] || 'Super Admin' }}</p>
      </div>
    </div>

    <!-- Collapsed user avatar -->
    <div class="user-collapsed" *ngIf="collapsed">
      <div class="avatar-small">
        <mat-icon>account_circle</mat-icon>
      </div>
    </div>

    <!-- Menu items -->
    <div class="menu-container">
      <ng-container *ngFor="let item of menuItems; let i = index">
        <!-- Add divider between main menu sections (except before the first item) -->
        <div *ngIf="i > 0 && (hasPermission(item) && !item.children) || (hasPermission(item) && hasAnyChildWithPermission(item) && item.children)" class="menu-divider"></div>

        <!-- Simple menu item with no children -->
        <div *ngIf="hasPermission(item) && !item.children"
             class="menu-item-container">
          <a class="menu-item"
             [routerLink]="item.route"
             routerLinkActive="active-link"
             [routerLinkActiveOptions]="{exact: item.route === '/admin/dashboard'}"
             [matTooltip]="collapsed ? item.name : ''"
             matTooltipPosition="right"
             matRipple>
            <div class="left-content">
              <div class="menu-icon" [matBadge]="getBadgeNumber(item)" [matBadgeColor]="item.badgeColor" [matBadgeHidden]="!item.badge">
                <mat-icon>{{ item.icon }}</mat-icon>
              </div>
              <span class="menu-label" *ngIf="!collapsed">{{ item.name }}</span>
            </div>
            <div class="active-indicator"></div>
          </a>

          <!-- Add action button -->
          <a *ngIf="item.addAction && hasAddActionPermission(item.addAction) && !collapsed"
             [routerLink]="item.addAction.route"
             [matTooltip]="item.addAction.tooltip"
             matTooltipPosition="right"
             routerLinkActive="active-add-button"
             class="add-button">
            <mat-icon>{{ item.addAction.icon }}</mat-icon>
          </a>
        </div>

        <!-- Menu item with children -->
        <div *ngIf="hasPermission(item) && hasAnyChildWithPermission(item) && item.children" class="menu-group">
          <!-- Parent menu item -->
          <div class="menu-item parent-item"
               (click)="toggleSubmenu(item, $event)"
               [class.active]="item.expanded || isMenuActive(item)"
               [matTooltip]="collapsed ? item.name : ''"
               matTooltipPosition="right"
               matRipple>
            <div class="left-content">
              <div class="menu-icon">
                <mat-icon>{{ item.icon }}</mat-icon>
              </div>
              <span class="menu-label" *ngIf="!collapsed">{{ item.name }}</span>
            </div>
            <mat-icon class="expand-icon" *ngIf="!collapsed" [@rotateIcon]="item.expanded ? 'expanded' : 'collapsed'">
              expand_more
            </mat-icon>
          </div>

          <!-- Submenu container -->
          <div class="submenu"
               [@submenuAnimation]="item.expanded ? 'visible' : 'hidden'"
               [class.collapsed-menu]="collapsed && (item.expanded || isMenuActive(item))">
            <!-- Render child items -->
            <ng-container *ngFor="let child of item.children">
              <!-- Child menu item -->
              <div *ngIf="hasPermission(child)" class="submenu-item-container">
                <a class="submenu-item"
                   [routerLink]="child.route"
                   routerLinkActive="active-sublink"
                   [routerLinkActiveOptions]="{exact: true}"
                   matRipple>
                  <div class="menu-icon" [matBadge]="getBadgeNumber(child)" [matBadgeColor]="child.badgeColor" [matBadgeHidden]="!child.badge">
                    <mat-icon>{{ child.icon }}</mat-icon>
                  </div>
                  <span class="menu-label" *ngIf="!collapsed">{{ child.name }}</span>
                  <div class="active-indicator"></div>
                </a>

                <!-- Add action button for child item -->
                <a *ngIf="child.addAction && hasAddActionPermission(child.addAction) && !collapsed"
                   [routerLink]="child.addAction.route"
                   [matTooltip]="child.addAction.tooltip"
                   matTooltipPosition="right"
                   routerLinkActive="active-add-button"
                   class="add-button submenu-add-button">
                  <mat-icon>{{ child.addAction.icon }}</mat-icon>
                </a>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Footer section with collapse button -->
  <div class="sidebar-footer">
    <a class="menu-item" (click)="toggleCollapsed()" matRipple>
      <div class="menu-icon">
        <mat-icon>{{ collapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
      </div>
      <span class="menu-label" *ngIf="!collapsed">{{ collapsed ? 'Expand' : 'Collapse' }} Menu</span>
    </a>
  </div>
</aside>