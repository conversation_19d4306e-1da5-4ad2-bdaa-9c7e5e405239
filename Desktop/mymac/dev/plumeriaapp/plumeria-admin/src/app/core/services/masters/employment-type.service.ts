import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  EmploymentType,
  EmploymentTypeCreateRequest,
  EmploymentTypeUpdateRequest,
} from '../../models/masters/employment-type';

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class EmploymentTypeService {
  private apiUrl = `${environment.apiUrl}/masters/employment-types`;

  constructor(private http: HttpClient) {}

  /**
   * Get all employment types
   */
  getAllEmploymentTypes(
    includeInactive: boolean = false
  ): Observable<EmploymentType[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<EmploymentType[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          console.error('Error fetching employment types:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get employment type by ID
   */
  getEmploymentTypeById(id: number): Observable<EmploymentType> {
    return this.http
      .get<ApiResponse<EmploymentType>>(`${this.apiUrl}/${id}`)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Employment type not found');
        }),
        catchError((error) => {
          console.error(`Error fetching employment type with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Create a new employment type
   */
  createEmploymentType(
    employmentType: EmploymentTypeCreateRequest
  ): Observable<EmploymentType> {
    return this.http
      .post<ApiResponse<EmploymentType>>(this.apiUrl, employmentType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to create employment type'
          );
        }),
        catchError((error) => {
          console.error('Error creating employment type:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing employment type
   */
  updateEmploymentType(
    id: number,
    employmentType: EmploymentTypeUpdateRequest
  ): Observable<EmploymentType> {
    return this.http
      .put<ApiResponse<EmploymentType>>(`${this.apiUrl}/${id}`, employmentType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update employment type'
          );
        }),
        catchError((error) => {
          console.error(`Error updating employment type with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete an employment type
   */
  deleteEmploymentType(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting employment type with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle employment type status (active/inactive)
   */
  toggleEmploymentTypeStatus(
    id: number,
    isActive: boolean
  ): Observable<EmploymentType> {
    return this.http
      .patch<ApiResponse<EmploymentType>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update employment type status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling status for employment type with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete employment types
   */
  bulkDeleteEmploymentTypes(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting employment types:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
