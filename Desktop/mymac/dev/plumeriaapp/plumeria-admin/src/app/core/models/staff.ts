export interface Staff {
  id: number;
  staff_name: string;
  staff_mobile: string;
  staff_email: string;
  gender: 'Male' | 'Female' | 'Other';
  user_role_id?: number;
  user_role_name?: string;
  date_of_birth: string;
  marital_status: 'Single' | 'Married' | 'Divorced' | 'Widowed';
  blood_group_id?: number;
  blood_group_name?: string;
  joining_date: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relation: string;
  designation_id: number;
  designation_name?: string;
  designation_code?: string;
  employment_type_id: number;
  employment_type_name?: string;
  health_insurance_provider?: string;
  health_insurance_number?: string;
  department_id: number;
  department_name?: string;
  department_code?: string;
  salary_amount?: number;
  salary_last_hiked_date?: string;
  staff_address: string;
  locality_id: number;
  locality_name?: string;
  city_id: number;
  city_name?: string;
  pincode: string;
  state_id: number;
  state_name?: string;
  aadhaar_number?: string;
  pan_number?: string;
  qualification_id?: number;
  qualification_name?: string;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  profile_picture?: string;
  // Authentication fields for mobile app
  password?: string;
  password_reset_token?: string;
  password_reset_expires?: string;
  last_login?: string;
  login_attempts?: number;
  account_locked_until?: string;
  mobile_verified?: boolean;
  mobile_verification_token?: string;
  mobile_verification_expires?: string;
  is_active: boolean;
  created_by: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at: string;
  updated_at: string;
}

export interface StaffCreateRequest {
  staff_name: string;
  staff_mobile: string;
  staff_email: string;
  gender: 'Male' | 'Female' | 'Other';
  user_role_id?: number;
  date_of_birth: string;
  marital_status: 'Single' | 'Married' | 'Divorced' | 'Widowed';
  blood_group_id?: number;
  joining_date: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relation: string;
  designation_id: number;
  employment_type_id: number;
  health_insurance_provider?: string;
  health_insurance_number?: string;
  department_id: number;
  salary_amount?: number;
  salary_last_hiked_date?: string;
  staff_address: string;
  locality_id: number;
  city_id: number;
  pincode: string;
  state_id: number;
  aadhaar_number?: string;
  pan_number?: string;
  qualification_id?: number;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  is_active?: boolean;
}

export interface StaffUpdateRequest extends Partial<StaffCreateRequest> {
  id: number;
}

export interface StaffListResponse {
  success: boolean;
  data: Staff[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message?: string;
}

export interface StaffResponse {
  success: boolean;
  data: Staff;
  message?: string;
}

export interface StaffFilters {
  search?: string;
  departmentId?: number;
  designationId?: number;
  includeInactive?: boolean;
  page?: number;
  limit?: number;
}

// Mobile Authentication Interfaces
export interface StaffLoginRequest {
  staff_mobile: string;
  password: string;
}

export interface StaffLoginResponse {
  success: boolean;
  data?: {
    staff: Staff;
    token: string;
    refreshToken?: string;
  };
  message?: string;
}

export interface StaffOTPRequest {
  staff_mobile: string;
}

export interface StaffOTPVerifyRequest {
  staff_mobile: string;
  otp: string;
}

export interface StaffPasswordSetRequest {
  staff_mobile: string;
  password: string;
  confirmPassword: string;
}

export interface StaffPasswordResetRequest {
  staff_mobile: string;
}

export interface StaffPasswordResetVerifyRequest {
  staff_mobile: string;
  token: string;
  newPassword: string;
  confirmPassword: string;
}
