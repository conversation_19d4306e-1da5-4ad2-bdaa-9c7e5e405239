import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, tap, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

// ProductCategory interface matching backend model
export interface ProductCategory {
  id?: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ProductCategoryService {
  private apiUrl = `${environment.apiUrl}/masters/product-categories`;

  constructor(private http: HttpClient) {}

  /**
   * Get all product categories
   */
  getAllProductCategories(
    includeInactive: boolean = false
  ): Observable<ProductCategory[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<ProductCategory[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get a product category by ID
   */
  getProductCategoryById(id: number): Observable<ProductCategory> {
    return this.http
      .get<ApiResponse<ProductCategory>>(`${this.apiUrl}/${id}`)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Product category not found');
        }),
        catchError((error) => {
          console.error(
            `Error fetching product category with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Create a new product category
   */
  createProductCategory(
    productCategory: ProductCategory
  ): Observable<ProductCategory> {
    return this.http
      .post<ApiResponse<ProductCategory>>(this.apiUrl, productCategory)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to create product category'
          );
        }),
        catchError((error) => {
          console.error('Error creating product category:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing product category
   */
  updateProductCategory(
    id: number,
    productCategory: ProductCategory
  ): Observable<ProductCategory> {
    return this.http
      .put<ApiResponse<ProductCategory>>(
        `${this.apiUrl}/${id}`,
        productCategory
      )
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update product category'
          );
        }),
        catchError((error) => {
          console.error(
            `Error updating product category with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete a product category
   */
  deleteProductCategory(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting product category with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle product category active status
   */
  toggleProductCategoryStatus(
    id: number,
    isActive: boolean
  ): Observable<ProductCategory> {
    return this.http
      .patch<ApiResponse<ProductCategory>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update product category status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling product category status with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete product categories
   */
  bulkDeleteProductCategories(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting product categories:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
