export interface Qualification {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QualificationCreateRequest {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface QualificationUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  is_active?: boolean;
}
