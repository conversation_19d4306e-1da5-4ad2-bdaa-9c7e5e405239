import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface MeasurementUnit {
  id: number;
  name: string;
  symbol: string;
  is_active: boolean;
  created_by: number;
  updated_by?: number;
  created_at: string;
  updated_at?: string;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface MeasurementUnitResponse {
  success: boolean;
  data: MeasurementUnit[];
  message?: string;
}

export interface SingleMeasurementUnitResponse {
  success: boolean;
  data: MeasurementUnit;
  message?: string;
}

export interface CreateMeasurementUnitRequest {
  name: string;
  symbol: string;
  is_active?: boolean;
}

export interface UpdateMeasurementUnitRequest {
  name?: string;
  symbol?: string;
  is_active?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MeasurementUnitService {
  private apiUrl = `${environment.apiUrl}/masters/measurement-units`;

  constructor(private http: HttpClient) {}

  // Get all measurement units
  getMeasurementUnits(includeInactive: boolean = false): Observable<MeasurementUnitResponse> {
    let params = new HttpParams();
    if (includeInactive) {
      params = params.set('includeInactive', 'true');
    }
    return this.http.get<MeasurementUnitResponse>(this.apiUrl, { params });
  }

  // Get measurement unit by ID
  getMeasurementUnitById(id: number): Observable<SingleMeasurementUnitResponse> {
    return this.http.get<SingleMeasurementUnitResponse>(`${this.apiUrl}/${id}`);
  }

  // Create new measurement unit
  createMeasurementUnit(measurementUnit: CreateMeasurementUnitRequest): Observable<SingleMeasurementUnitResponse> {
    return this.http.post<SingleMeasurementUnitResponse>(this.apiUrl, measurementUnit);
  }

  // Update measurement unit
  updateMeasurementUnit(id: number, measurementUnit: UpdateMeasurementUnitRequest): Observable<SingleMeasurementUnitResponse> {
    return this.http.put<SingleMeasurementUnitResponse>(`${this.apiUrl}/${id}`, measurementUnit);
  }

  // Delete measurement unit
  deleteMeasurementUnit(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  // Toggle measurement unit status
  toggleMeasurementUnitStatus(id: number, isActive: boolean): Observable<SingleMeasurementUnitResponse> {
    return this.http.patch<SingleMeasurementUnitResponse>(`${this.apiUrl}/${id}/status`, { is_active: isActive });
  }

  // Bulk delete measurement units
  bulkDeleteMeasurementUnits(ids: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-delete`, { ids });
  }
}
