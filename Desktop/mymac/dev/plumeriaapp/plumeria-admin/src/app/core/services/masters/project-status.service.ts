// src/app/core/services/masters/project-status.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

// ProjectStatus interface matching backend model
export interface ProjectStatus {
  id?: number;
  name: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ProjectStatusService {
  private apiUrl = `${environment.apiUrl}/masters/project-statuses`;

  constructor(private http: HttpClient) {}

  /**
   * Get all project statuses
   */
  getAllProjectStatuses(
    includeInactive: boolean = false
  ): Observable<ProjectStatus[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<ProjectStatus[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          console.error('Error fetching project statuses:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get project status by ID
   */
  getProjectStatusById(id: number): Observable<ProjectStatus> {
    return this.http
      .get<ApiResponse<ProjectStatus>>(`${this.apiUrl}/${id}`)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error('Project status not found');
        }),
        catchError((error) => {
          console.error(`Error fetching project status with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Create a new project status
   */
  createProjectStatus(projectStatus: ProjectStatus): Observable<ProjectStatus> {
    return this.http
      .post<ApiResponse<ProjectStatus>>(this.apiUrl, projectStatus)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to create project status'
          );
        }),
        catchError((error) => {
          console.error('Error creating project status:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing project status
   */
  updateProjectStatus(
    id: number,
    projectStatus: ProjectStatus
  ): Observable<ProjectStatus> {
    return this.http
      .put<ApiResponse<ProjectStatus>>(`${this.apiUrl}/${id}`, projectStatus)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update project status'
          );
        }),
        catchError((error) => {
          console.error(`Error updating project status with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete a project status
   */
  deleteProjectStatus(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting project status with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle project status active status
   */
  toggleProjectStatusStatus(
    id: number,
    isActive: boolean
  ): Observable<ProjectStatus> {
    return this.http
      .patch<ApiResponse<ProjectStatus>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update project status status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling status for project status with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete project statuses
   */
  bulkDeleteProjectStatuses(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting project statuses:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
