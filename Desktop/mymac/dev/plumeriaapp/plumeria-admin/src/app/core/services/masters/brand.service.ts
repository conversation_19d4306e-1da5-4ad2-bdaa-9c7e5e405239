import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

// Brand interface matching backend model
export interface Brand {
  id?: number;
  name: string;
  product_subcategory_id: number;
  product_subcategory_name?: string;
  product_category_name?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class BrandService {
  private apiUrl = `${environment.apiUrl}/masters/brands`;

  constructor(private http: HttpClient) {}

  /**
   * Get all brands
   */
  getAllBrands(includeInactive: boolean = false): Observable<Brand[]> {
    let params = new HttpParams().set('includeInactive', includeInactive.toString());

    return this.http.get<ApiResponse<Brand[]>>(this.apiUrl, { params }).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError((error) => {
        console.error('Error fetching brands:', error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Get brand by ID
   */
  getBrandById(id: number): Observable<Brand> {
    return this.http.get<ApiResponse<Brand>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Brand not found');
      }),
      catchError((error) => {
        console.error(`Error fetching brand with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Get brands by product subcategory
   */
  getBrandsBySubcategory(subcategoryId: number, includeInactive: boolean = false): Observable<Brand[]> {
    let params = new HttpParams().set('includeInactive', includeInactive.toString());

    return this.http.get<ApiResponse<Brand[]>>(`${this.apiUrl}/subcategory/${subcategoryId}`, { params }).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError((error) => {
        console.error(`Error fetching brands for subcategory ${subcategoryId}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Create a new brand
   */
  createBrand(brand: Brand): Observable<Brand> {
    return this.http.post<ApiResponse<Brand>>(this.apiUrl, brand).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to create brand');
      }),
      catchError((error) => {
        console.error('Error creating brand:', error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Update an existing brand
   */
  updateBrand(id: number, brand: Brand): Observable<Brand> {
    return this.http.put<ApiResponse<Brand>>(`${this.apiUrl}/${id}`, brand).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update brand');
      }),
      catchError((error) => {
        console.error(`Error updating brand with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Delete a brand
   */
  deleteBrand(id: number): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to delete brand');
        }
      }),
      catchError((error) => {
        console.error(`Error deleting brand with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle brand active status
   */
  toggleBrandStatus(id: number): Observable<Brand> {
    return this.http.patch<ApiResponse<Brand>>(`${this.apiUrl}/${id}/status`, {}).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update brand status');
      }),
      catchError((error) => {
        console.error(`Error toggling brand status with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Extract error message from HTTP error response
   */
  private getErrorMessage(error: any): string {
    if (error.error?.message) {
      return error.error.message;
    }
    if (error.message) {
      return error.message;
    }
    if (typeof error.error === 'string') {
      return error.error;
    }
    return 'An unexpected error occurred';
  }
}
