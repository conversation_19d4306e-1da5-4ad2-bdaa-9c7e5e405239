import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Staff,
  StaffCreateRequest,
  StaffUpdateRequest,
  StaffListResponse,
  StaffResponse,
  StaffFilters,
  StaffLoginRequest,
  StaffLoginResponse,
  StaffOTPRequest,
  StaffOTPVerifyRequest,
  StaffPasswordSetRequest,
  StaffPasswordResetRequest,
  StaffPasswordResetVerifyRequest,
} from '../models/staff';

@Injectable({
  providedIn: 'root',
})
export class StaffService {
  private apiUrl = `${environment.apiUrl}/staff`;

  constructor(private http: HttpClient) {}

  /**
   * Get all staff with optional filtering and pagination
   */
  getStaff(filters: StaffFilters = {}): Observable<StaffListResponse> {
    let params = new HttpParams();

    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.departmentId) {
      params = params.set('departmentId', filters.departmentId.toString());
    }
    if (filters.designationId) {
      params = params.set('designationId', filters.designationId.toString());
    }
    if (filters.includeInactive !== undefined) {
      params = params.set(
        'includeInactive',
        filters.includeInactive.toString()
      );
    }
    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }

    return this.http.get<StaffListResponse>(this.apiUrl, { params });
  }

  /**
   * Get staff by ID
   */
  getStaffById(id: number): Observable<StaffResponse> {
    return this.http.get<StaffResponse>(`${this.apiUrl}/${id}`);
  }

  /**
   * Create new staff member
   */
  createStaff(staff: StaffCreateRequest): Observable<StaffResponse> {
    return this.http.post<StaffResponse>(this.apiUrl, staff);
  }

  /**
   * Update staff member
   */
  updateStaff(
    id: number,
    staff: StaffUpdateRequest
  ): Observable<StaffResponse> {
    return this.http.put<StaffResponse>(`${this.apiUrl}/${id}`, staff);
  }

  /**
   * Delete staff member
   */
  deleteStaff(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  /**
   * Toggle staff active status
   */
  toggleStaffStatus(id: number, isActive: boolean): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, {
      is_active: isActive,
    });
  }

  /**
   * Bulk delete staff members
   */
  bulkDeleteStaff(ids: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-delete`, { ids });
  }

  /**
   * Bulk update staff status
   */
  bulkUpdateStatus(ids: number[], isActive: boolean): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-status`, {
      ids,
      is_active: isActive,
    });
  }

  /**
   * Get staff by IDs
   */
  getStaffByIds(ids: number[]): Observable<StaffListResponse> {
    return this.http.post<StaffListResponse>(`${this.apiUrl}/by-ids`, { ids });
  }

  /**
   * Search staff
   */
  searchStaff(query: string): Observable<StaffListResponse> {
    let params = new HttpParams();
    if (query) {
      params = params.set('q', query);
    }
    return this.http.get<StaffListResponse>(`${this.apiUrl}/search`, {
      params,
    });
  }

  /**
   * Get staff statistics
   */
  getStaffStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/stats`);
  }

  /**
   * Export staff data
   */
  exportStaff(format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    let params = new HttpParams();
    params = params.set('format', format);

    return this.http.get(`${this.apiUrl}/export`, {
      params,
      responseType: 'blob',
    });
  }

  /**
   * Get staff by department
   */
  getStaffByDepartment(
    departmentId: number,
    includeInactive = false
  ): Observable<StaffListResponse> {
    let params = new HttpParams();
    if (includeInactive) {
      params = params.set('includeInactive', 'true');
    }

    return this.http.get<StaffListResponse>(
      `${this.apiUrl}/department/${departmentId}`,
      { params }
    );
  }

  /**
   * Upload profile picture
   */
  uploadProfilePicture(id: number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('profile_picture', file);

    return this.http.post(`${this.apiUrl}/${id}/upload-photo`, formData);
  }

  /**
   * Remove profile picture
   */
  removeProfilePicture(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}/photo`);
  }

  /**
   * Get profile picture URL
   */
  getProfilePictureUrl(profilePicture: string): string {
    if (!profilePicture || !profilePicture.trim()) {
      return '';
    }

    // Clean the profile picture path
    const cleanPath = profilePicture.trim();

    // If it's already a full URL, return as is
    if (cleanPath.startsWith('http://') || cleanPath.startsWith('https://')) {
      return cleanPath;
    }

    // Get base URL and ensure it doesn't end with slash
    const baseUrl = environment.apiUrl.replace('/api', '').replace(/\/$/, '');

    // Handle different path formats that might come from the API
    let finalPath = cleanPath;
    
    // If the path doesn't start with slash, add it
    if (!finalPath.startsWith('/')) {
      finalPath = `/${finalPath}`;
    }

    // Handle both uploads/staff/ and staffs/ formats
    // Convert uploads/staff/filename.jpg to /staffs/filename.jpg for consistency
    if (finalPath.includes('/uploads/staff/')) {
      finalPath = finalPath.replace('/uploads/staff/', '/staffs/');
    }

    const fullUrl = `${baseUrl}${finalPath}`;

    console.log('Profile picture URL construction:', {
      original: profilePicture,
      cleaned: cleanPath,
      baseUrl,
      finalPath,
      fullUrl
    });

    return fullUrl;
  }

  // Staff Authentication Methods (for mobile app management)

  /**
   * Staff login (for mobile app)
   */
  staffLogin(loginData: StaffLoginRequest): Observable<StaffLoginResponse> {
    return this.http.post<StaffLoginResponse>(
      `${this.apiUrl}/auth/login`,
      loginData
    );
  }

  /**
   * Send OTP to staff mobile
   */
  sendOTP(otpData: StaffOTPRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/send-otp`, otpData);
  }

  /**
   * Verify OTP and login
   */
  verifyOTP(verifyData: StaffOTPVerifyRequest): Observable<StaffLoginResponse> {
    return this.http.post<StaffLoginResponse>(
      `${this.apiUrl}/auth/verify-otp`,
      verifyData
    );
  }

  /**
   * Set password for staff (first time setup)
   */
  setPassword(passwordData: StaffPasswordSetRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/set-password`, passwordData);
  }

  /**
   * Request password reset
   */
  requestPasswordReset(resetData: StaffPasswordResetRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/reset-password`, resetData);
  }

  /**
   * Verify password reset and set new password
   */
  verifyPasswordReset(
    verifyData: StaffPasswordResetVerifyRequest
  ): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/verify-reset`, verifyData);
  }

  /**
   * Refresh JWT token
   */
  refreshToken(refreshToken: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/refresh-token`, {
      refreshToken,
    });
  }
}
