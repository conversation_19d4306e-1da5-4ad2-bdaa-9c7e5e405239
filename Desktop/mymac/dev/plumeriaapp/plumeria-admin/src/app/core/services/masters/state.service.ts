import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { State, StateResponse } from '../../models/masters/state';

@Injectable({
  providedIn: 'root',
})
export class StateService {
  private apiUrl = `${environment.apiUrl}/masters/states`;

  constructor(private http: HttpClient) {}

  // Get all states
  getStates(
    includeInactive = false,
    countryId?: number
  ): Observable<StateResponse> {
    let url = `${this.apiUrl}?includeInactive=${includeInactive}`;

    if (countryId) {
      url += `&countryId=${countryId}`;
    }

    return this.http.get<StateResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get state by ID
  getStateById(id: number): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.get<StateResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Create new state
  createState(state: Partial<State>): Observable<StateResponse> {
    return this.http.post<StateResponse>(this.apiUrl, state).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Update state
  updateState(id: number, state: Partial<State>): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.put<StateResponse>(url, state).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Delete state
  deleteState(id: number): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;

    return this.http.delete<StateResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Toggle state status
  toggleStateStatus(id: number, isActive: boolean): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}/status`;

    return this.http.patch<StateResponse>(url, { is_active: isActive }).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Bulk delete states
  bulkDeleteStates(ids: number[]): Observable<StateResponse> {
    const url = `${this.apiUrl}/bulk-delete`;

    return this.http.post<StateResponse>(url, { ids }).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }
}
