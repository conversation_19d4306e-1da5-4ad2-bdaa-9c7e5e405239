// src/app/core/services/masters/supplier-type.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

// SupplierType interface matching backend model
export interface SupplierType {
  id?: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class SupplierTypeService {
  private apiUrl = `${environment.apiUrl}/masters/supplier-types`;

  constructor(private http: HttpClient) {}

  /**
   * Get all supplier types
   */
  getAllSupplierTypes(
    includeInactive: boolean = false
  ): Observable<SupplierType[]> {
    let params = new HttpParams().set(
      'includeInactive',
      includeInactive.toString()
    );

    return this.http
      .get<ApiResponse<SupplierType[]>>(this.apiUrl, { params })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError((error) => {
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Get a supplier type by ID
   */
  getSupplierTypeById(id: number): Observable<SupplierType> {
    return this.http
      .get<ApiResponse<SupplierType>>(`${this.apiUrl}/${id}`)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Supplier type not found');
        }),
        catchError((error) => {
          console.error(`Error fetching supplier type with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Create a new supplier type
   */
  createSupplierType(supplierType: SupplierType): Observable<SupplierType> {
    return this.http
      .post<ApiResponse<SupplierType>>(this.apiUrl, supplierType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to create supplier type');
        }),
        catchError((error) => {
          console.error('Error creating supplier type:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Update an existing supplier type
   */
  updateSupplierType(
    id: number,
    supplierType: SupplierType
  ): Observable<SupplierType> {
    return this.http
      .put<ApiResponse<SupplierType>>(`${this.apiUrl}/${id}`, supplierType)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to update supplier type');
        }),
        catchError((error) => {
          console.error(`Error updating supplier type with ID ${id}:`, error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Delete a supplier type
   */
  deleteSupplierType(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting supplier type with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle supplier type active status
   */
  toggleSupplierTypeStatus(
    id: number,
    isActive: boolean
  ): Observable<SupplierType> {
    return this.http
      .patch<ApiResponse<SupplierType>>(`${this.apiUrl}/${id}/status`, {
        is_active: isActive,
      })
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(
            response.message || 'Failed to update supplier type status'
          );
        }),
        catchError((error) => {
          console.error(
            `Error toggling supplier type status with ID ${id}:`,
            error
          );
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Bulk delete supplier types
   */
  bulkDeleteSupplierTypes(ids: number[]): Observable<boolean> {
    return this.http
      .post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids })
      .pipe(
        map((response) => {
          return response.success;
        }),
        catchError((error) => {
          console.error('Error bulk deleting supplier types:', error);
          return throwError(() => new Error(this.getErrorMessage(error)));
        })
      );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
