<div class="container">
  <div class="loading-shade" *ngIf="loading">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div *ngIf="!loading && measurementUnit">
    <!-- Header Card -->
    <mat-card class="header-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>straighten</mat-icon>
          {{ measurementUnit.name }}
        </mat-card-title>
        <mat-card-subtitle>
          Measurement Unit Details
        </mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-actions class="header-actions">
        <button mat-raised-button color="primary" (click)="navigateToEdit()">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button 
                [color]="measurementUnit.is_active ? 'warn' : 'accent'"
                (click)="toggleStatus()">
          <mat-icon>{{ measurementUnit.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
          {{ measurementUnit.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteMeasurementUnit()">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="navigateToList()">
          <mat-icon>arrow_back</mat-icon> Back to List
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Details Card -->
    <mat-card class="details-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>info</mat-icon>
          Basic Information
        </mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="details-grid">
          <div class="detail-item">
            <label>ID:</label>
            <span>{{ measurementUnit.id }}</span>
          </div>
          
          <div class="detail-item">
            <label>Name:</label>
            <span class="name-value">{{ measurementUnit.name }}</span>
          </div>
          
          <div class="detail-item">
            <label>Symbol:</label>
            <span class="symbol-value">{{ measurementUnit.symbol }}</span>
          </div>
          
          <div class="detail-item">
            <label>Status:</label>
            <span [class]="measurementUnit.is_active ? 'active-status' : 'inactive-status'">
              <mat-icon>{{ measurementUnit.is_active ? 'check_circle' : 'cancel' }}</mat-icon>
              {{ measurementUnit.is_active ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Audit Information Card -->
    <mat-card class="audit-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Audit Information
        </mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="audit-grid">
          <div class="audit-section">
            <h6>Created</h6>
            <div class="audit-item">
              <label>Created By:</label>
              <span>{{ measurementUnit.created_by_username || 'N/A' }}</span>
            </div>
            <div class="audit-item">
              <label>Created At:</label>
              <span>{{ measurementUnit.created_at | date:'medium' }}</span>
            </div>
          </div>
          
          <div class="audit-section" *ngIf="measurementUnit.updated_at">
            <h6>Last Updated</h6>
            <div class="audit-item">
              <label>Updated By:</label>
              <span>{{ measurementUnit.updated_by_username || 'N/A' }}</span>
            </div>
            <div class="audit-item">
              <label>Updated At:</label>
              <span>{{ measurementUnit.updated_at | date:'medium' }}</span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && !measurementUnit" class="error-state">
    <mat-card>
      <mat-card-content>
        <div class="error-content">
          <mat-icon>error_outline</mat-icon>
          <h3>Measurement Unit Not Found</h3>
          <p>The requested measurement unit could not be found.</p>
          <button mat-raised-button color="primary" (click)="navigateToList()">
            <mat-icon>arrow_back</mat-icon> Back to List
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
