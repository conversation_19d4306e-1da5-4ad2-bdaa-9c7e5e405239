import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import {
  ProjectStatusService,
  ProjectStatus,
} from '../../../../core/services/masters/project-status.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-project-status-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './project-status-list.component.html',
  styleUrls: ['./project-status-list.component.scss'],
})
export class ProjectStatusListComponent implements OnInit {
  projectStatuses: ProjectStatus[] = [];
  filteredProjectStatuses: ProjectStatus[] = [];
  displayedProjectStatuses: ProjectStatus[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<ProjectStatus>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalProjectStatuses = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private projectStatusService: ProjectStatusService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProjectStatuses();
  }

  loadProjectStatuses(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.projectStatusService.getAllProjectStatuses(false).subscribe({
      next: (projectStatuses) => {
        this.projectStatuses = projectStatuses;
        this.totalProjectStatuses = projectStatuses.length;
        this.applyFilter();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load project statuses: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredProjectStatuses = this.projectStatuses.filter(
      (status) =>
        status.name.toLowerCase().includes(filterValue) ||
        (status.created_by_username &&
          status.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalProjectStatuses = this.filteredProjectStatuses.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedProjectStatuses();
  }

  updateDisplayedProjectStatuses(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedProjectStatuses = this.filteredProjectStatuses.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedProjectStatuses();
  }

  toggleStatus(projectStatus: ProjectStatus): void {
    const newStatus = !projectStatus.is_active;

    this.projectStatusService
      .toggleProjectStatusStatus(projectStatus.id!, newStatus)
      .subscribe({
        next: (updatedStatus) => {
          projectStatus.is_active = updatedStatus.is_active;
          this.showSnackBar(
            `Project Status ${projectStatus.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
          // Revert toggle in UI
          projectStatus.is_active = !newStatus;
        },
      });
  }

  deleteProjectStatus(projectStatus: ProjectStatus): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the project status "${projectStatus.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.projectStatusService
          .deleteProjectStatus(projectStatus.id!)
          .subscribe({
            next: () => {
              this.projectStatuses = this.projectStatuses.filter(
                (s) => s.id !== projectStatus.id
              );
              this.applyFilter();
              this.showSnackBar(
                `Project Status ${projectStatus.name} deleted successfully`
              );
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedProjectStatuses = this.selection.selected;

    if (selectedProjectStatuses.length === 0) {
      this.showSnackBar('No project statuses selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedProjectStatuses.length} selected project statuses?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedProjectStatuses.map((status) => status.id!);

        this.projectStatusService.bulkDeleteProjectStatuses(ids).subscribe({
          next: () => {
            this.projectStatuses = this.projectStatuses.filter(
              (status) => !ids.includes(status.id!)
            );
            this.selection.clear();
            this.applyFilter();
            this.showSnackBar(
              `Successfully deleted ${ids.length} project statuses`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedProjectStatuses.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedProjectStatuses);
    }
  }

  refreshList(): void {
    this.loadProjectStatuses();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
