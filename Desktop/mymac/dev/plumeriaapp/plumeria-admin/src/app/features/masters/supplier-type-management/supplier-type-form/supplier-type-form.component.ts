// Debug-enhanced supplier-type-form.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import {
  SupplierTypeService,
  SupplierType,
} from '../../../../core/services/masters/supplier-type.service';

@Component({
  selector: 'app-supplier-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './supplier-type-form.component.html',
  styleUrls: ['./supplier-type-form.component.scss'],
})
export class SupplierTypeFormComponent implements OnInit {
  supplierTypeForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  supplierTypeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private supplierTypeService: SupplierTypeService
  ) {}

  ngOnInit(): void {
    console.log('SupplierTypeFormComponent initialized');
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.supplierTypeId = +id;
        this.isEditMode = true;
        console.log(
          `Edit mode activated, loading supplier type ID: ${this.supplierTypeId}`
        );
        this.loadSupplierType(this.supplierTypeId);
      }
    });
  }

  initForm(): void {
    this.supplierTypeForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(500)],
      is_active: [true],
    });
    console.log('Form initialized');
  }

  loadSupplierType(id: number): void {
    this.isLoading = true;
    console.log(`Loading supplier type data for ID: ${id}`);

    this.supplierTypeService.getSupplierTypeById(id).subscribe({
      next: (supplierType) => {
        console.log('Supplier type data received:', supplierType);
        this.supplierTypeForm.patchValue(supplierType);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading supplier type:', error);
        this.errorMessage = `Failed to load supplier type: ${this.getErrorMessage(
          error
        )}`;
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.supplierTypeForm.invalid) {
      console.log('Form is invalid, not submitting');
      return;
    }

    this.isSubmitting = true;
    const formData = this.supplierTypeForm.value;
    console.log('Submitting form with data:', formData);

    if (this.isEditMode && this.supplierTypeId) {
      // Update existing supplier type
      console.log(`Updating supplier type ID: ${this.supplierTypeId}`);

      this.supplierTypeService
        .updateSupplierType(this.supplierTypeId, formData)
        .subscribe({
          next: (updatedType) => {
            console.log('Update successful:', updatedType);
            this.isSubmitting = false;
            this.showSnackBar(`Supplier Type updated successfully`);

            // Use replaceUrl and catchError pattern to prevent navigation issues
            try {
              console.log('Navigating back to supplier types list');
              this.router.navigate(['../../'], { relativeTo: this.route });
            } catch (navError) {
              console.error('Navigation error:', navError);
            }
          },
          error: (error) => {
            console.error('Update error details:', error);
            this.isSubmitting = false;
            this.errorMessage = `Failed to update supplier type: ${this.getErrorMessage(
              error
            )}`;
            this.showSnackBar(this.errorMessage, true);
          },
        });
    } else {
      // Create new supplier type
      console.log('Creating new supplier type');

      this.supplierTypeService.createSupplierType(formData).subscribe({
        next: (newType) => {
          console.log('Create successful:', newType);
          this.isSubmitting = false;
          this.showSnackBar(`Supplier Type created successfully`);

          // Use replaceUrl and catchError pattern to prevent navigation issues
          try {
            console.log('Navigating back to supplier types list');
            this.router.navigate(['../ '], { relativeTo: this.route });
          } catch (navError) {
            console.error('Navigation error:', navError);
          }
        },
        error: (error) => {
          console.error('Create error details:', error);
          this.isSubmitting = false;
          this.errorMessage = `Failed to create supplier type: ${this.getErrorMessage(
            error
          )}`;
          this.showSnackBar(this.errorMessage, true);
        },
      });
    }
  }

  onCancel(): void {
    console.log('Cancelling form, navigating back to list');
    this.router.navigate(['/masters/supplier-types']);
  }

  showSnackBar(message: string, isError = false): void {
    console.log(`Showing snackbar: ${message}, isError: ${isError}`);
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  // Helper method to extract better error messages
  private getErrorMessage(error: any): string {
    console.log('Extracting error message from:', error);

    if (error instanceof HttpErrorResponse) {
      // For HTTP errors
      if (error.status === 401) {
        return 'Your session has expired. Please log in again.';
      } else if (error.status === 403) {
        return 'You do not have permission to perform this action.';
      } else if (error.status === 0) {
        return 'Could not connect to the server. Please check your internet connection.';
      } else if (error.error && error.error.message) {
        return error.error.message;
      }
    }

    // For other types of errors or fallback
    return error.message || 'An unknown error occurred';
  }
}
