<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} Project Status</mat-card-title>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
    </div>
    
    <mat-card-content *ngIf="!isLoading">
      <form [formGroup]="projectStatusForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter project status name">
            <mat-error *ngIf="projectStatusForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
            <mat-error *ngIf="projectStatusForm.get('name')?.hasError('maxlength')">
              Name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row status-toggle">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ projectStatusForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
        
        <div class="form-actions">
          <button mat-raised-button type="button" (click)="onCancel()">Cancel</button>
          <button mat-raised-button color="primary" type="submit" [disabled]="projectStatusForm.invalid || isSubmitting">
            <mat-spinner diameter="20" *ngIf="isSubmitting"></mat-spinner>
            <span *ngIf="!isSubmitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>