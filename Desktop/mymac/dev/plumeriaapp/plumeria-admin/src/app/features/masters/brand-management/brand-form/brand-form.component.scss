.container {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

mat-card-header {
  margin-bottom: 20px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: red;
  margin-bottom: 15px;
}

.form-field {
  margin-bottom: 20px;
  width: 100%;
}

mat-form-field {
  width: 100%;
}

.toggle-field {
  margin-top: 10px;
  margin-bottom: 30px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}


