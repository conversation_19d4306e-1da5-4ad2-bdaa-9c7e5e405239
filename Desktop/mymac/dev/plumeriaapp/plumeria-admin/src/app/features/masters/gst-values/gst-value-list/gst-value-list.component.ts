import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import {
  GstValueService,
  GstValue,
} from '../../../../core/services/gst-value.service';

@Component({
  selector: 'app-gst-value-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MatPaginatorModule,
    MatSortModule,
  ],
  templateUrl: './gst-value-list.component.html',
  styleUrls: ['./gst-value-list.component.scss'],
})
export class GstValueListComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'value',
    'created_by',
    'status',
    'actions',
  ];
  displayedGstValues = new MatTableDataSource<GstValue>([]);
  selection = new SelectionModel<GstValue>(true, []);

  searchTerm = '';
  includeInactive = false;
  isLoading = false;
  errorMessage = '';

  // Pagination
  totalGstValues = 0;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];

  constructor(
    private gstValueService: GstValueService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadGstValues();
  }

  loadGstValues(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.gstValueService.getGstValues(this.includeInactive).subscribe({
      next: (response) => {
        if (response.success) {
          this.displayedGstValues.data = response.data;
          this.totalGstValues = response.data.length;
          this.applyFilter();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading GST values:', error);
        this.errorMessage = 'Failed to load GST values';
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.trim().toLowerCase();
    this.displayedGstValues.filter = filterValue;

    if (this.displayedGstValues.paginator) {
      this.displayedGstValues.paginator.firstPage();
    }
  }

  toggleIncludeInactive(): void {
    this.loadGstValues();
  }

  refreshList(): void {
    this.selection.clear();
    this.loadGstValues();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedGstValues.data.length;
    return numSelected === numRows;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }
    this.selection.select(...this.displayedGstValues.data);
  }

  // Action methods
  toggleStatus(gstValue: GstValue): void {
    const newStatus = !gstValue.is_active;
    this.gstValueService
      .toggleGstValueStatus(gstValue.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            gstValue.is_active = newStatus;
            this.showSnackBar(
              `GST value ${
                newStatus ? 'activated' : 'deactivated'
              } successfully`
            );
          }
        },
        error: (error) => {
          console.error('Error toggling GST value status:', error);
          this.showSnackBar('Failed to update GST value status', true);
        },
      });
  }

  deleteGstValue(gstValue: GstValue): void {
    if (
      confirm(`Are you sure you want to delete GST value ${gstValue.value}%?`)
    ) {
      this.gstValueService.deleteGstValue(gstValue.id).subscribe({
        next: (response) => {
          this.showSnackBar('GST value deleted successfully');
          this.loadGstValues();
        },
        error: (error) => {
          console.error('Error deleting GST value:', error);
          this.showSnackBar('Failed to delete GST value', true);
        },
      });
    }
  }

  bulkDeleteSelected(): void {
    const selectedGstValues = this.selection.selected;
    if (selectedGstValues.length === 0) return;

    const confirmMessage = `Are you sure you want to delete ${selectedGstValues.length} GST value(s)?`;
    if (confirm(confirmMessage)) {
      const ids = selectedGstValues.map((gv) => gv.id);
      this.gstValueService.bulkDeleteGstValues(ids).subscribe({
        next: (response) => {
          this.showSnackBar(
            `${selectedGstValues.length} GST value(s) deleted successfully`
          );
          this.selection.clear();
          this.loadGstValues();
        },
        error: (error) => {
          console.error('Error in bulk delete:', error);
          this.showSnackBar('Failed to delete GST values', true);
        },
      });
    }
  }

  // Pagination
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    // Handle pagination if needed
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
