import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  FormsModule,
  NgForm,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';
import { HttpClient, HttpClientModule } from '@angular/common/http';

import { LocalityService } from '../../../../core/services/masters/locality.service';
import { CityService } from '../../../../core/services/masters/city.service';
import { Locality } from '../../../../core/models/masters/locality';
import { City } from '../../../../core/models/masters/city';

@Component({
  selector: 'app-locality-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatSelectModule,
    HttpClientModule,
  ],
  templateUrl: './locality-form.component.html',
  styleUrls: ['./locality-form.component.scss'],
})
export class LocalityFormComponent implements OnInit {
  @ViewChild('localityFormElement') localityFormElement!: ElementRef;

  localityForm!: FormGroup;
  isEditMode = false;
  localityId: number | null = null;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  pageTitle = 'Add New Locality';
  submitButtonText = 'Create Locality';

  // For city dropdown
  cities: City[] = [];
  loadingCities = false;

  constructor(
    private fb: FormBuilder,
    private localityService: LocalityService,
    private cityService: CityService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private http: HttpClient
  ) {
    this.createForm();
  }

  ngOnInit(): void {
    this.loadCities();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.localityId = +id;
        this.pageTitle = 'Edit Locality';
        this.submitButtonText = 'Update Locality';
        this.loadLocality(+id);
      }
    });
  }

  createForm(): void {
    this.localityForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      city_id: [null, Validators.required],
      is_active: [true],
    });
  }

  loadLocality(id: number): void {
    this.isLoading = true;
    this.localityService.getLocalityById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          const locality = response.data;

          // Populate the form
          this.localityForm.patchValue({
            name: locality.name,
            city_id: locality.city_id,
            is_active: locality.is_active,
          });

          // We don't need to handle the code field here as it's not in the form
        } else {
          this.errorMessage = 'Failed to load locality details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading locality: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      },
    });
  }

  loadCities(): void {
    this.loadingCities = true;
    this.cityService.getCities(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = Array.isArray(response.data) ? response.data : [];
        } else {
          this.errorMessage = response.message || 'Failed to load cities';
          this.showSnackBar(this.errorMessage, true);
        }
        this.loadingCities = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading cities: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.loadingCities = false;
      },
    });
  }

  onSubmit(): void {
    console.log('Form submitted', this.localityForm);
    console.log('Form value:', this.localityForm.value);
    console.log('Form valid:', this.localityForm.valid);

    if (this.localityForm.invalid) {
      console.log(
        'Form is invalid. Validation errors:',
        this.getFormValidationErrors()
      );
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.localityForm.controls).forEach((key) => {
        const control = this.localityForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    console.log('Form is valid, proceeding with submission');

    this.isSubmitting = true;
    const localityData: Partial<Locality> = this.localityForm.value;

    // Add a default code value if not in edit mode (for new localities)
    // No code generation needed

    if (this.isEditMode && this.localityId) {
      // Update existing locality
      this.localityService
        .updateLocality(this.localityId, localityData)
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar('Locality updated successfully');
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.errorMessage =
                response.message || 'Failed to update locality';
              this.showSnackBar(this.errorMessage, true);
            }
            this.isSubmitting = false;
          },
          error: (error) => {
            this.errorMessage =
              'Error updating locality: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            this.isSubmitting = false;
          },
        });
    } else {
      // Create new locality
      console.log('Calling createLocality service with data:', localityData);
      this.localityService.createLocality(localityData).subscribe({
        next: (response) => {
          console.log('Create locality response:', response);
          if (response.success) {
            this.showSnackBar('Locality created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create locality';
            this.showSnackBar(this.errorMessage, true);
            console.error('API returned error:', this.errorMessage);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error creating locality:', error);
          this.errorMessage =
            'Error creating locality: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          this.isSubmitting = false;
        },
        complete: () => {
          console.log('Create locality request completed');
        },
      });
    }
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  // Helper method to get form validation errors for debugging
  getFormValidationErrors(): any {
    const errors: any = {};
    Object.keys(this.localityForm.controls).forEach((key) => {
      const control = this.localityForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }

  // Button click handler for debugging
  onButtonClick(event: Event): void {
    console.log('Button clicked', event);

    // Prevent the default button behavior
    event.preventDefault();

    // Manually trigger form submission
    console.log('Manually triggering form submission');
    this.onSubmit();
  }

  // Test method for direct API call
  testDirectApiCall(): void {
    console.log('Test direct API call button clicked');

    // Create a simple test locality object
    const testLocalityData: Partial<Locality> = {
      name: this.localityForm.get('name')?.value || 'Test Locality',
      city_id: this.localityForm.get('city_id')?.value || 1,
      is_active: true,
    };

    console.log('Test data:', testLocalityData);
    this.isSubmitting = true;

    // Make a direct HTTP request without using the service
    const apiUrl = 'http://localhost:3000/api/masters/localities';
    console.log('Making direct HTTP request to:', apiUrl);

    this.http.post(apiUrl, testLocalityData).subscribe({
      next: (response: any) => {
        console.log('Direct HTTP response:', response);
        this.showSnackBar('Test API call successful');
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Direct HTTP error:', error);
        this.errorMessage =
          'Test API call failed: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isSubmitting = false;
      },
    });
  }

  // Form validation helpers
  get nameControl() {
    return this.localityForm.get('name');
  }
  get cityIdControl() {
    return this.localityForm.get('city_id');
  }
}
