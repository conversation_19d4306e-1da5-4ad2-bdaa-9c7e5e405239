<div class="container">
  <mat-card class="list-card">
    <mat-card-header>
      <mat-card-title>Measurement Units</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="new">
          <mat-icon>add</mat-icon> Add Measurement Unit
        </button>
        <button mat-raised-button color="warn" [disabled]="selection.isEmpty()" (click)="bulkDeleteSelected()">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
        <button mat-icon-button (click)="refreshList()" matTooltip="Refresh List">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="filter-container">
      <mat-form-field appearance="outline">
        <mat-label>Search Measurement Units</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, symbol, or creator">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-slide-toggle [(ngModel)]="includeInactive" (change)="toggleIncludeInactive()" color="primary">
        Include Inactive
      </mat-slide-toggle>
    </div>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="refreshList()">Try Again</button>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="displayedMeasurementUnits" matSort>

        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? toggleAllRows() : null"
                         [checked]="selection.hasValue() && isAllSelected()"
                         [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
          <td mat-cell *matCellDef="let measurementUnit"> {{ measurementUnit.id }} </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
          <td mat-cell *matCellDef="let measurementUnit"> 
            <strong>{{ measurementUnit.name }}</strong>
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="symbol">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Symbol </th>
          <td mat-cell *matCellDef="let measurementUnit"> 
            <code>{{ measurementUnit.symbol }}</code>
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
          <td mat-cell *matCellDef="let measurementUnit"> {{ measurementUnit.created_by_username || 'N/A' }} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td mat-cell *matCellDef="let measurementUnit"> 
            <span [class]="measurementUnit.is_active ? 'active-status' : 'inactive-status'">
              {{ measurementUnit.is_active ? 'Active' : 'Inactive' }}
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let measurementUnit">
            <button mat-icon-button [routerLink]="[measurementUnit.id]" matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button [routerLink]="['edit', measurementUnit.id]" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="toggleStatus(measurementUnit)" matTooltip="{{ measurementUnit.is_active ? 'Deactivate' : 'Activate' }}">
              <mat-icon>{{ measurementUnit.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteMeasurementUnit(measurementUnit)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- Row shown when there is no matching data -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="7">No measurement units found</td>
        </tr>
      </table>
    </div>

    <mat-paginator [length]="totalMeasurementUnits"
                  [pageSize]="pageSize"
                  [pageSizeOptions]="pageSizeOptions"
                  (page)="onPageChange($event)">
    </mat-paginator>
  </mat-card>
</div>
