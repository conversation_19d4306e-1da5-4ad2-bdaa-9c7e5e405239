.container {
  padding: 20px;
}

.form-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 20px auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.help-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  margin-bottom: 20px;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  padding: 15px;
  margin-bottom: 20px;
  background-color: #ffebee;
  border-radius: 4px;
}

.error-message {
  color: #f44336;
  margin: 0;
}

.form-row {
  margin-bottom: 20px;

  mat-form-field {
    width: 100%;
  }
}

.status-toggle {
  margin-top: 10px;
  margin-bottom: 30px;

  .status-label {
    display: block;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    margin-bottom: 8px;
  }

  mat-slide-toggle {
    display: block;
    margin-bottom: 8px;
  }

  .status-hint {
    mat-hint {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;

  button {
    min-width: 100px;
  }
}

/* Button with spinner */
button mat-spinner {
  display: inline-block;
  margin-right: 5px;
}

/* Help section styling */
.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 15px 0;
}

.example-category {
  h6 {
    color: #1976d2;
    margin-bottom: 8px;
    font-weight: 600;
  }
}

.help-list {
  margin: 0;
  padding-left: 15px;

  li {
    margin-bottom: 4px;
    font-size: 14px;

    strong {
      color: #333;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-card, .help-card {
    max-width: 100%;
  }

  .examples-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .help-list {
    padding-left: 15px;
  }

  .form-actions {
    flex-direction: column;

    button {
      width: 100%;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
