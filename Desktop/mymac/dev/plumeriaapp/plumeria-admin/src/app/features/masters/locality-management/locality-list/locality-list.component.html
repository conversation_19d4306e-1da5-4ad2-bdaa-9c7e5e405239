<div class="locality-list-container">
  <div class="list-header">
    <h1>Localities</h1>
    <button mat-raised-button color="primary" routerLink="new">
      <mat-icon>add</mat-icon> Add Locality
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Search and filter fields -->
      <div class="filter-container">
        <div class="search-field">
          <mat-form-field appearance="outline">
            <mat-label>Search Localities</mat-label>
            <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, city, state, or country">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="country-filter">
          <mat-form-field appearance="outline">
            <mat-label>Filter by Country</mat-label>
            <mat-select [(ngModel)]="selectedCountryId" (selectionChange)="onCountryChange()">
              <mat-option>None</mat-option>
              <mat-option *ngFor="let country of countries" [value]="country.id">
                {{ country.name }}
              </mat-option>
            </mat-select>
            <mat-icon matSuffix *ngIf="loadingCountries">sync</mat-icon>
            <button *ngIf="selectedCountryId" matSuffix mat-icon-button aria-label="Clear" (click)="clearCountryFilter()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="state-filter">
          <mat-form-field appearance="outline">
            <mat-label>Filter by State</mat-label>
            <mat-select [(ngModel)]="selectedStateId" (selectionChange)="onStateChange()">
              <mat-option>None</mat-option>
              <mat-option *ngFor="let state of states" [value]="state.id">
                {{ state.name }}
              </mat-option>
            </mat-select>
            <mat-icon matSuffix *ngIf="loadingStates">sync</mat-icon>
            <button *ngIf="selectedStateId" matSuffix mat-icon-button aria-label="Clear" (click)="clearStateFilter()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="city-filter">
          <mat-form-field appearance="outline">
            <mat-label>Filter by City</mat-label>
            <mat-select [(ngModel)]="selectedCityId" (selectionChange)="onCityChange()">
              <mat-option>None</mat-option>
              <mat-option *ngFor="let city of cities" [value]="city.id">
                {{ city.name }}
              </mat-option>
            </mat-select>
            <mat-icon matSuffix *ngIf="loadingCities">sync</mat-icon>
            <button *ngIf="selectedCityId" matSuffix mat-icon-button aria-label="Clear" (click)="clearCityFilter()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <div class="left-actions">
          <button mat-raised-button color="warn" (click)="bulkDeleteSelected()"
                  [disabled]="selection.selected.length === 0"
                  matTooltip="Delete selected localities">
            <mat-icon>delete</mat-icon> Delete Selected ({{ selection.selected.length }})
          </button>

          <mat-slide-toggle
            [(ngModel)]="includeInactive"
            (change)="toggleIncludeInactive()"
            matTooltip="Include inactive localities"
            class="show-inactive-toggle">
            Show Inactive
          </mat-slide-toggle>

          <button mat-icon-button (click)="refreshList()" matTooltip="Refresh list">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>

      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading localities...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Success message -->
      <div class="success-message" *ngIf="successMessage">
        <mat-icon>check_circle</mat-icon> {{ successMessage }}
      </div>

      <!-- No data message -->
      <div class="no-data-message" *ngIf="!isLoading && filteredLocalities.length === 0">
        <mat-icon>info</mat-icon>
        <p>No localities found. Try adjusting your search or filters.</p>
      </div>

      <!-- Data table -->
      <div class="table-container mat-elevation-z2" *ngIf="!isLoading && filteredLocalities.length > 0">
        <table mat-table [dataSource]="displayedLocalities" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? toggleAllRows() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let locality">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(locality) : null"
                [checked]="selection.isSelected(locality)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let locality"> {{locality.id}} </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
            <td mat-cell *matCellDef="let locality"> {{locality.name}} </td>
          </ng-container>

          <!-- Code Column removed -->

          <!-- City Column -->
          <ng-container matColumnDef="city_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> City </th>
            <td mat-cell *matCellDef="let locality"> {{locality.city_name}} </td>
          </ng-container>

          <!-- State Column -->
          <ng-container matColumnDef="state_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> State </th>
            <td mat-cell *matCellDef="let locality"> {{locality.state_name}} </td>
          </ng-container>

          <!-- Country Column -->
          <ng-container matColumnDef="country_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Country </th>
            <td mat-cell *matCellDef="let locality"> {{locality.country_name}} </td>
          </ng-container>

          <!-- Created By Column -->
          <ng-container matColumnDef="created_by">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let locality"> {{locality.created_by_username || 'N/A'}} </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let locality">
              <span [ngClass]="locality.is_active ? 'status-active' : 'status-inactive'">
                {{ locality.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let locality">
              <button mat-icon-button (click)="toggleStatus(locality)"
                      [matTooltip]="locality.is_active ? 'Deactivate' : 'Activate'">
                <mat-icon>{{ locality.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="[locality.id]" matTooltip="View details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="['edit', locality.id]" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button (click)="deleteLocality(locality)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Paginator -->
        <mat-paginator
          [length]="totalLocalities"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
