<div class="container">
  <mat-card class="list-card">
    <mat-card-header>
      <mat-card-title>Product Subcategories</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="new" matTooltip="Add New Product Subcategory">
          <mat-icon>add</mat-icon> Add New
        </button>
        <button mat-raised-button color="warn" [disabled]="selection.isEmpty()" (click)="bulkDeleteSelected()" matTooltip="Delete Selected">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
        <button mat-icon-button (click)="refreshList()" matTooltip="Refresh List">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, description, category or creator">
          <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <!-- Add toggle for showing inactive items -->
        <mat-slide-toggle [(ngModel)]="includeInactive" (change)="toggleIncludeInactive()">
          Include Inactive
        </mat-slide-toggle>
      </div>

      <div class="loading-shade" *ngIf="isLoading">
        <mat-spinner diameter="50"></mat-spinner>
      </div>

      <div class="error-container" *ngIf="!isLoading && errorMessage">
        <p class="error-message">{{ errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="refreshList()">
          <mat-icon>refresh</mat-icon> Try Again
        </button>
      </div>

      <div class="table-container" *ngIf="!isLoading && !errorMessage">
        <table mat-table [dataSource]="displayedProductSubcategories" matSort class="mat-elevation-z8">

          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? toggleAllRows() : null"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let productSubcategory"> {{ productSubcategory.id }} </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
            <td mat-cell *matCellDef="let productSubcategory"> {{ productSubcategory.name }} </td>
          </ng-container>

          <!-- Category Column -->
          <ng-container matColumnDef="category_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Category </th>
            <td mat-cell *matCellDef="let productSubcategory"> {{ productSubcategory.category_name || 'N/A' }} </td>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
            <td mat-cell *matCellDef="let productSubcategory"> {{ productSubcategory.description || 'N/A' }} </td>
          </ng-container>

          <!-- Created By Column -->
          <ng-container matColumnDef="created_by">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let productSubcategory"> {{ productSubcategory.created_by_username || 'N/A' }} </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let productSubcategory">
              <span [ngClass]="productSubcategory.is_active ? 'active-status' : 'inactive-status'">
                {{ productSubcategory.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let productSubcategory">
              <button mat-icon-button [routerLink]="[productSubcategory.id]" matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="['edit', productSubcategory.id]" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button (click)="toggleProductSubcategoryStatus(productSubcategory)" matTooltip="{{ productSubcategory.is_active ? 'Deactivate' : 'Activate' }}">
                <mat-icon>{{ productSubcategory.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
              </button>
              <button mat-icon-button (click)="deleteProductSubcategory(productSubcategory)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          <!-- Row shown when there is no matching data. -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">No product subcategories found</td>
          </tr>
        </table>

        <mat-paginator
          [length]="totalProductSubcategories"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
