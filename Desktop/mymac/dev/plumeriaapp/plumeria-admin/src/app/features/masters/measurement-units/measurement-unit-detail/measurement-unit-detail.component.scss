.container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.loading-shade {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  padding: 16px;
}

.details-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.audit-card {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  
  mat-icon {
    color: #1976d2;
  }
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  label {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
  }
  
  span {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.87);
  }
}

.name-value {
  font-weight: 600;
  color: #1976d2;
}

.symbol-value {
  font-family: 'Courier New', monospace;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  color: #d63384;
  display: inline-block;
}

.active-status {
  color: #4caf50;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
}

.inactive-status {
  color: #f44336;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
}

.audit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 16px;
}

.audit-section {
  h6 {
    color: #1976d2;
    margin-bottom: 12px;
    font-weight: 600;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 4px;
  }
}

.audit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.6);
    min-width: 100px;
  }
  
  span {
    color: rgba(0, 0, 0, 0.87);
    text-align: right;
  }
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-content {
  text-align: center;
  padding: 40px;
  
  mat-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #f44336;
    margin-bottom: 16px;
  }
  
  h3 {
    color: rgba(0, 0, 0, 0.87);
    margin-bottom: 8px;
  }
  
  p {
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 24px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .header-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
  
  .details-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .audit-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .audit-item {
    flex-direction: column;
    align-items: flex-start;
    
    label {
      min-width: auto;
      margin-bottom: 4px;
    }
    
    span {
      text-align: left;
    }
  }
}
