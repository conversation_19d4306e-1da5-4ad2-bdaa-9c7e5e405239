import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { SelectionModel } from '@angular/cdk/collections';
import {
  ProductSubcategoryService,
  ProductSubcategory,
} from '../../../../core/services/masters/product-subcategory.service';

import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-subcategory-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './product-subcategory-list.component.html',
  styleUrl: './product-subcategory-list.component.scss',
})
export class ProductSubcategoryListComponent implements OnInit {
  productSubcategories: ProductSubcategory[] = [];
  filteredProductSubcategories: ProductSubcategory[] = [];
  displayedProductSubcategories: ProductSubcategory[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'category_name',
    'description',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<ProductSubcategory>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalProductSubcategories = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private productSubcategoryService: ProductSubcategoryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProductSubcategories();
  }

  loadProductSubcategories(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.productSubcategoryService
      .getAllProductSubcategories(this.includeInactive)
      .subscribe({
        next: (productSubcategories) => {
          this.productSubcategories = productSubcategories;
          this.totalProductSubcategories = productSubcategories.length;
          this.applyFilter();
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = `Failed to load product subcategories: ${error.message}`;
          this.isLoading = false;
        },
      });
  }

  toggleIncludeInactive(): void {
    // Reload product subcategories when the toggle changes
    this.loadProductSubcategories();
  }

  applyFilter(): void {
    if (!this.searchTerm) {
      this.filteredProductSubcategories = [...this.productSubcategories];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase();
      this.filteredProductSubcategories = this.productSubcategories.filter(
        (productSubcategory) =>
          productSubcategory.name.toLowerCase().includes(searchTermLower) ||
          (productSubcategory.description &&
            productSubcategory.description
              .toLowerCase()
              .includes(searchTermLower)) ||
          (productSubcategory.category_name &&
            productSubcategory.category_name
              .toLowerCase()
              .includes(searchTermLower)) ||
          (productSubcategory.created_by_username &&
            productSubcategory.created_by_username
              .toLowerCase()
              .includes(searchTermLower))
      );
    }

    this.totalProductSubcategories = this.filteredProductSubcategories.length;
    this.updateDisplayedProductSubcategories();
  }

  updateDisplayedProductSubcategories(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedProductSubcategories =
      this.filteredProductSubcategories.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedProductSubcategories();
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedProductSubcategories.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedProductSubcategories);
    }
  }

  refreshList(): void {
    this.selection.clear();
    this.searchTerm = '';
    this.loadProductSubcategories();
  }

  toggleProductSubcategoryStatus(productSubcategory: ProductSubcategory): void {
    const newStatus = !productSubcategory.is_active;

    this.productSubcategoryService
      .toggleProductSubcategoryStatus(productSubcategory.id!, newStatus)
      .subscribe({
        next: (updatedProductSubcategory) => {
          // Update the product subcategory in the list
          const index = this.productSubcategories.findIndex(
            (psc) => psc.id === updatedProductSubcategory.id
          );
          if (index !== -1) {
            this.productSubcategories[index] = updatedProductSubcategory;
            this.applyFilter();
          }

          this.snackBar.open(
            `Product subcategory ${updatedProductSubcategory.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`,
            'Close',
            { duration: 3000 }
          );
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to update status: ${this.getErrorMessage(error)}`,
            'Close',
            { duration: 5000 }
          );
        },
      });
  }

  deleteProductSubcategory(productSubcategory: ProductSubcategory): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the product subcategory "${productSubcategory.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.productSubcategoryService
          .deleteProductSubcategory(productSubcategory.id!)
          .subscribe({
            next: (success) => {
              if (success) {
                // Remove the deleted product subcategory from the list
                this.productSubcategories = this.productSubcategories.filter(
                  (psc) => psc.id !== productSubcategory.id
                );
                this.selection.deselect(productSubcategory);
                this.applyFilter();

                this.snackBar.open(
                  `Product subcategory ${productSubcategory.name} deleted successfully`,
                  'Close',
                  { duration: 3000 }
                );
              }
            },
            error: (error) => {
              this.snackBar.open(
                `Failed to delete product subcategory: ${this.getErrorMessage(
                  error
                )}`,
                'Close',
                { duration: 5000 }
              );
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedProductSubcategories = this.selection.selected;
    if (selectedProductSubcategories.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedProductSubcategories.length} selected product subcategory(s)?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedProductSubcategories.map(
          (productSubcategory) => productSubcategory.id!
        );

        this.productSubcategoryService
          .bulkDeleteProductSubcategories(ids)
          .subscribe({
            next: (success) => {
              if (success) {
                // Remove the deleted product subcategories from the list
                this.productSubcategories = this.productSubcategories.filter(
                  (productSubcategory) => !ids.includes(productSubcategory.id!)
                );
                this.selection.clear();
                this.applyFilter();

                this.snackBar.open(
                  `${ids.length} product subcategory(s) deleted successfully`,
                  'Close',
                  { duration: 3000 }
                );
              }
            },
            error: (error) => {
              this.snackBar.open(
                `Failed to delete product subcategories: ${this.getErrorMessage(
                  error
                )}`,
                'Close',
                { duration: 5000 }
              );
            },
          });
      }
    });
  }

  getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
