.gst-value-detail-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  
  .header-content {
    .page-title {
      color: #333;
      font-weight: 600;
      margin-bottom: 10px;
      
      i {
        margin-right: 10px;
        color: #007bff;
      }
    }
    
    .breadcrumb {
      background: none;
      padding: 0;
      margin: 0;
      
      .breadcrumb-item {
        a {
          color: #007bff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        &.active {
          color: #6c757d;
        }
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.detail-container {
  .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
    
    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 15px 20px;
      
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-title {
          color: #495057;
          font-weight: 600;
          
          i {
            margin-right: 8px;
            color: #007bff;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 8px;
          
          .btn {
            padding: 6px 12px;
            font-size: 13px;
            
            i {
              margin-right: 4px;
            }
          }
        }
      }
    }
    
    .card-body {
      padding: 25px;
    }
  }
  
  .main-info-card {
    .info-item {
      margin-bottom: 20px;
      
      .info-label {
        display: block;
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        font-size: 14px;
        
        i {
          margin-right: 6px;
          color: #6c757d;
          width: 14px;
        }
      }
      
      .info-value {
        font-size: 16px;
        color: #333;
        
        &.gst-value-display {
          font-size: 24px;
          font-weight: 700;
          color: #007bff;
        }
        
        .badge {
          font-size: 12px;
          padding: 6px 12px;
          border-radius: 12px;
          
          &.badge-success {
            background-color: #28a745;
            color: white;
          }
          
          &.badge-secondary {
            background-color: #6c757d;
            color: white;
          }
        }
      }
    }
  }
  
  .audit-info-card {
    .audit-section {
      .audit-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 15px;
        
        i {
          margin-right: 8px;
        }
      }
      
      .audit-details {
        .audit-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #f1f3f4;
          
          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }
          
          .audit-label {
            font-weight: 500;
            color: #6c757d;
            font-size: 14px;
          }
          
          .audit-value {
            color: #333;
            font-size: 14px;
            text-align: right;
          }
        }
      }
    }
  }
}

.navigation-actions {
  margin-top: 30px;
  text-align: center;
  
  .btn {
    padding: 10px 20px;
    font-weight: 500;
    
    i {
      margin-right: 8px;
    }
  }
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  
  .error-content {
    text-align: center;
    max-width: 400px;
    
    i {
      margin-bottom: 20px;
    }
    
    h4 {
      color: #495057;
      margin-bottom: 10px;
    }
    
    p {
      margin-bottom: 20px;
      line-height: 1.5;
    }
    
    .btn {
      padding: 10px 20px;
      font-weight: 500;
      
      i {
        margin-right: 8px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .gst-value-detail-container {
    padding: 15px;
  }
  
  .detail-container {
    .card .card-header .header-content {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
      
      .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
    
    .card .card-body {
      padding: 20px 15px;
    }
    
    .audit-info-card .audit-details .audit-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      
      .audit-value {
        text-align: left;
      }
    }
  }
}

@media (max-width: 576px) {
  .detail-container .card .card-header .header-content .action-buttons {
    .btn {
      flex: 1;
      min-width: 0;
      
      span {
        display: none;
      }
    }
  }
}
