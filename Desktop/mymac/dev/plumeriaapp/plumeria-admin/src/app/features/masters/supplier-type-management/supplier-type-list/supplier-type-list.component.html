<!-- src/app/features/masters/supplier-type/supplier-type-list/supplier-type-list.component.html -->
<div class="container">
  <mat-card class="list-card">
    <mat-card-header>
      <mat-card-title>Supplier Types</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" routerLink="new">
          <mat-icon>add</mat-icon> Add Supplier Type
        </button>
        <button mat-raised-button color="warn" [disabled]="selection.isEmpty()" (click)="bulkDeleteSelected()">
          <mat-icon>delete</mat-icon> Delete Selected
        </button>
        <button mat-icon-button (click)="refreshList()" matTooltip="Refresh List">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="filter-container">
      <mat-form-field appearance="outline">
        <mat-label>Search Supplier Types</mat-label>
        <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, description, or creator">
        <button *ngIf="searchTerm" matSuffix mat-icon-button aria-label="Clear" (click)="searchTerm=''; applyFilter()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-slide-toggle [(ngModel)]="includeInactive" (change)="toggleIncludeInactive()" color="primary">
        Include Inactive
      </mat-slide-toggle>
    </div>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="refreshList()">Try Again</button>
    </div>

    <div class="table-container">
      <table mat-table [dataSource]="displayedSupplierTypes" matSort>

        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? toggleAllRows() : null"
                         [checked]="selection.hasValue() && isAllSelected()"
                         [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                         (change)="$event ? selection.toggle(row) : null"
                         [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
          <td mat-cell *matCellDef="let supplierType"> {{ supplierType.id }} </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
          <td mat-cell *matCellDef="let supplierType"> {{ supplierType.name }} </td>
        </ng-container>

        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
          <td mat-cell *matCellDef="let supplierType"> 
            {{ (supplierType.description && supplierType.description.length > 50) ? 
               (supplierType.description | slice:0:50) + '...' : 
               (supplierType.description || 'No description provided') }} 
          </td>
        </ng-container>

        <!-- Created By Column -->
        <ng-container matColumnDef="created_by">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
          <td mat-cell *matCellDef="let supplierType"> {{ supplierType.created_by_username || 'N/A' }} </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td mat-cell *matCellDef="let supplierType"> 
            <span [class]="supplierType.is_active ? 'active-status' : 'inactive-status'">
              {{ supplierType.is_active ? 'Active' : 'Inactive' }}
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let supplierType">
            <button mat-icon-button [routerLink]="[supplierType.id]" matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button [routerLink]="['edit', supplierType.id]" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button (click)="toggleStatus(supplierType)" matTooltip="{{ supplierType.is_active ? 'Deactivate' : 'Activate' }}">
              <mat-icon>{{ supplierType.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
            </button>
            <button mat-icon-button (click)="deleteSupplierType(supplierType)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- Row shown when there is no matching data -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="7">No supplier types found</td>
        </tr>
      </table>
    </div>

    <mat-paginator [length]="totalSupplierTypes"
                  [pageSize]="pageSize"
                  [pageSizeOptions]="pageSizeOptions"
                  (page)="onPageChange($event)">
    </mat-paginator>
  </mat-card>
</div>