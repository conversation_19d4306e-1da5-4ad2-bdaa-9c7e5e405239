<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Product Subcategory Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" [routerLink]="['../edit', productSubcategory.id]" matTooltip="Edit Product Subcategory" *ngIf="productSubcategory">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="accent" (click)="toggleProductSubcategoryStatus()" matTooltip="{{ productSubcategory.is_active ? 'Deactivate' : 'Activate' }}" *ngIf="productSubcategory">
          <mat-icon>{{ productSubcategory.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          {{ productSubcategory.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteProductSubcategory()" matTooltip="Delete Product Subcategory">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-icon-button routerLink="../.." matTooltip="Back to List">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="!isLoading && errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" routerLink="../..">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage && productSubcategory">
      <div class="detail-section">
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ productSubcategory.id }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ productSubcategory.name }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Product Category:</div>
          <div class="detail-value">{{ productSubcategory.category_name || 'N/A' }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Description:</div>
          <div class="detail-value">{{ productSubcategory.description || 'N/A' }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Status:</div>
          <div class="detail-value">
            <mat-chip [ngClass]="productSubcategory.is_active ? 'active-chip' : 'inactive-chip'">
              {{ productSubcategory.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ productSubcategory.created_by_username || 'N/A' }}</div>
        </div>

        <div class="detail-row" *ngIf="productSubcategory.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ productSubcategory.created_at | date:'medium' }}</div>
        </div>

        <div class="detail-row" *ngIf="productSubcategory.updated_by_username">
          <div class="detail-label">Updated By:</div>
          <div class="detail-value">{{ productSubcategory.updated_by_username }}</div>
        </div>

        <div class="detail-row" *ngIf="productSubcategory.updated_at">
          <div class="detail-label">Updated At:</div>
          <div class="detail-value">{{ productSubcategory.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
