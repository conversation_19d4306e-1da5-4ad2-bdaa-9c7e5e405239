// src/app/features/masters/supplier-type/supplier-type-view/supplier-type-view.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import {
  SupplierTypeService,
  SupplierType,
} from '../../../../core/services/masters/supplier-type.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-supplier-type-view',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './supplier-type-view.component.html',
  styleUrls: ['./supplier-type-view.component.scss'],
})
export class SupplierTypeViewComponent implements OnInit {
  supplierType: SupplierType | null = null;
  isLoading = false;
  errorMessage = '';
  supplierTypeId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private supplierTypeService: SupplierTypeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.supplierTypeId = +id;
        this.loadSupplierType(this.supplierTypeId);
      } else {
        this.errorMessage = 'Supplier Type ID not provided';
        this.isLoading = false;
      }
    });
  }

  loadSupplierType(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.supplierTypeService.getSupplierTypeById(id).subscribe({
      next: (supplierType) => {
        this.supplierType = supplierType;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load supplier type: ${error.message}`;
        this.isLoading = false;
        this.showSnackBar(this.errorMessage, true);
      },
    });
  }

  editSupplierType(): void {
    if (this.supplierTypeId) {
      this.router.navigate([
        '/masters/supplier-types/edit',
        this.supplierTypeId,
      ]);
    }
  }

  deleteSupplierType(): void {
    if (!this.supplierType) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the supplier type "${this.supplierType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.supplierTypeId) {
        this.supplierTypeService
          .deleteSupplierType(this.supplierTypeId)
          .subscribe({
            next: () => {
              this.showSnackBar(
                `Supplier Type ${this.supplierType?.name} deleted successfully`
              );
              this.router.navigate(['/masters/supplier-types']);
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  toggleStatus(): void {
    if (!this.supplierType || !this.supplierTypeId) return;

    const newStatus = !this.supplierType.is_active;

    this.supplierTypeService
      .toggleSupplierTypeStatus(this.supplierTypeId, newStatus)
      .subscribe({
        next: (updatedType) => {
          this.supplierType!.is_active = updatedType.is_active;
          this.showSnackBar(
            `Supplier Type ${this.supplierType?.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
        },
      });
  }

  goBack(): void {
    this.router.navigate(['/masters/supplier-types']);
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
