/* src/app/features/masters/supplier-type/supplier-type-view/supplier-type-view.component.scss */
.container {
  padding: 20px;
}

.detail-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: #f44336;
  margin-bottom: 15px;
}

.detail-section {
  padding: 10px;
}

.status-chip {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 500;
  width: 150px;
  color: #555;
}

.detail-value {
  flex: 1;
}

mat-divider {
  margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .detail-card {
    max-width: 100%;
  }
  
  .header-actions {
    flex-wrap: wrap;
  }
  
  .detail-row {
    flex-direction: column;
  }
  
  .detail-label {
    width: 100%;
    margin-bottom: 5px;
  }
}